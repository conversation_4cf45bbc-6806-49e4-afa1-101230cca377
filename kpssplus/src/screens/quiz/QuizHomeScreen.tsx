import React from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import {StackScreenProps} from '@react-navigation/stack';
import {CompositeScreenProps} from '@react-navigation/native';
import {BottomTabScreenProps} from '@react-navigation/bottom-tabs';
import {QuizStackParamList, MainTabParamList, RootStackParamList} from '../../navigation/types';
import {Button, Card} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
import {useAuth, useGuestMode} from '../../hooks';
import Icon from 'react-native-vector-icons/Ionicons';

type Props = CompositeScreenProps<
  StackScreenProps<QuizStackParamList, 'QuizHome'>,
  CompositeScreenProps<
    BottomTabScreenProps<MainTabParamList, 'Quiz'>,
    StackScreenProps<RootStackParamList>
  >
>;

export const QuizHomeScreen: React.FC<Props> = ({navigation}) => {
  const {isAuthenticated, isGuest} = useAuth();
  const {getRemainingQuizAttempts, checkQuizLimit} = useGuestMode();

  const handleQuizPress = (quizId: string) => {
    if (isGuest && checkQuizLimit()) {
      // Show guest limit modal
      navigation.navigate('QuizDetail', {quizId});
    } else {
      navigation.navigate('QuizDetail', {quizId});
    }
  };

  const handleSearchPress = () => {
    navigation.navigate('QuizSearch', {});
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Guest Mode Banner */}
        {isGuest && (
          <Card style={styles.guestBanner} variant="outlined">
            <Text style={styles.guestTitle}>Misafir Modundasınız</Text>
            <Text style={styles.guestText}>
              {checkQuizLimit()
                ? 'Quiz çözme limitiniz doldu. Tüm quizlere erişmek için giriş yapın.'
                : `Kalan quiz hakkınız: ${getRemainingQuizAttempts()}`}
            </Text>
            <Button
              title="Giriş Yap"
              onPress={() => {
                navigation.navigate('Auth', {
                  screen: 'Login',
                });
              }}
              size="small"
              style={styles.loginButton}
            />
          </Card>
        )}

        {/* Search Bar */}
        <Card style={styles.searchCard} onPress={handleSearchPress}>
          <View style={styles.searchContent}>
            <Icon
              name="search-outline"
              size={20}
              color={colors.text.tertiary}
            />
            <Text style={styles.searchText}>Quiz ara...</Text>
          </View>
        </Card>

        {/* Stats Cards */}
        {isAuthenticated && (
          <View style={styles.statsContainer}>
            <Card style={styles.statCard} variant="elevated">
              <Text style={styles.statNumber}>12</Text>
              <Text style={styles.statLabel}>Tamamlanan</Text>
            </Card>
            <Card style={styles.statCard} variant="elevated">
              <Text style={styles.statNumber}>85%</Text>
              <Text style={styles.statLabel}>Ortalama Başarı</Text>
            </Card>
            <Card style={styles.statCard} variant="elevated">
              <Text style={styles.statNumber}>3</Text>
              <Text style={styles.statLabel}>Günlük Streak</Text>
            </Card>
          </View>
        )}

        {/* Popular Quizzes Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Popüler Quizler</Text>

          {[1, 2, 3, 4, 5].map(item => (
            <Card
              key={item}
              style={styles.quizCard}
              variant="elevated"
              onPress={() => handleQuizPress(`quiz-${item}`)}>
              <View style={styles.quizHeader}>
                <View style={styles.quizInfo}>
                  <Text style={styles.quizTitle}>Matematik Quiz {item}</Text>
                  <Text style={styles.quizSubject}>Matematik</Text>
                </View>
                <View style={styles.difficultyBadge}>
                  <Text style={styles.difficultyText}>Orta</Text>
                </View>
              </View>

              <View style={styles.quizMeta}>
                <View style={styles.metaItem}>
                  <Icon
                    name="clipboard-outline"
                    size={16}
                    color={colors.text.secondary}
                  />
                  <Text style={styles.metaText}>{10 + item} Soru</Text>
                </View>
                <View style={styles.metaItem}>
                  <Icon
                    name="time-outline"
                    size={16}
                    color={colors.text.secondary}
                  />
                  <Text style={styles.metaText}>{15 + item * 5} dk</Text>
                </View>
                <View style={styles.metaItem}>
                  <Icon
                    name="people-outline"
                    size={16}
                    color={colors.text.secondary}
                  />
                  <Text style={styles.metaText}>{100 + item * 50} kişi</Text>
                </View>
              </View>

              <View style={styles.quizFooter}>
                <Text style={styles.averageScore}>
                  Ortalama: {75 + item * 3}%
                </Text>
                <Button
                  title="Başla"
                  onPress={() => handleQuizPress(`quiz-${item}`)}
                  size="small"
                  style={styles.startButton}
                />
              </View>
            </Card>
          ))}
        </View>

        {/* Recent Results Section */}
        {isAuthenticated && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Son Sonuçlarım</Text>

            {[1, 2, 3].map(item => (
              <Card
                key={item}
                style={styles.resultCard}
                variant="elevated"
                onPress={() =>
                  navigation.navigate('QuizResult', {
                    resultId: `result-${item}`,
                  })
                }>
                <View style={styles.resultHeader}>
                  <Text style={styles.resultTitle}>Fizik Quiz {item}</Text>
                  <Text style={styles.resultScore}>{80 + item * 5}%</Text>
                </View>
                <View style={styles.resultMeta}>
                  <Text style={styles.resultDate}>2 gün önce</Text>
                  <Text style={styles.resultRank}>#{item * 5}. sırada</Text>
                </View>
              </Card>
            ))}
          </View>
        )}

        {/* Categories Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Kategoriler</Text>

          <View style={styles.categoriesGrid}>
            {[
              'Matematik',
              'Fizik',
              'Kimya',
              'Biyoloji',
              'Tarih',
              'Coğrafya',
            ].map(category => (
              <Card
                key={category}
                style={styles.categoryCard}
                variant="outlined"
                onPress={() => handleSearchPress()}>
                <Text style={styles.categoryTitle}>{category}</Text>
                <Text style={styles.categoryCount}>
                  {Math.floor(Math.random() * 20) + 5} Quiz
                </Text>
              </Card>
            ))}
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: spacing[4],
  },
  guestBanner: {
    marginVertical: spacing[4],
    backgroundColor: colors.warning.warningMuted,
    borderColor: colors.warning.warning,
  },
  guestTitle: {
    ...typography.h6,
    color: colors.warning.warningDark,
    marginBottom: spacing[2],
  },
  guestText: {
    ...typography.body2,
    color: colors.warning.warningDark,
    marginBottom: spacing[3],
  },
  loginButton: {
    alignSelf: 'flex-start',
  },
  searchCard: {
    marginVertical: spacing[2],
    backgroundColor: colors.gray[100],
  },
  searchContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchText: {
    ...typography.body1,
    color: colors.text.tertiary,
    marginLeft: spacing[2],
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: spacing[4],
  },
  statCard: {
    flex: 1,
    marginHorizontal: spacing[1],
    alignItems: 'center',
    paddingVertical: spacing[4],
  },
  statNumber: {
    ...typography.h3,
    color: colors.primary,
    marginBottom: spacing[1],
  },
  statLabel: {
    ...typography.caption,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  section: {
    marginVertical: spacing[4],
  },
  sectionTitle: {
    ...typography.h5,
    marginBottom: spacing[3],
  },
  quizCard: {
    marginBottom: spacing[3],
  },
  quizHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing[3],
  },
  quizInfo: {
    flex: 1,
  },
  quizTitle: {
    ...typography.cardTitle,
    marginBottom: spacing[1],
  },
  quizSubject: {
    ...typography.caption,
    color: colors.primary,
  },
  difficultyBadge: {
    backgroundColor: colors.warning.warningMuted,
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: 4,
  },
  difficultyText: {
    ...typography.caption,
    color: colors.warning.warningDark,
    fontWeight: '500',
  },
  quizMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing[3],
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaText: {
    ...typography.caption,
    color: colors.text.secondary,
    marginLeft: spacing[1],
  },
  quizFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  averageScore: {
    ...typography.body2,
    color: colors.text.secondary,
  },
  startButton: {
    paddingHorizontal: spacing[4],
  },
  resultCard: {
    marginBottom: spacing[2],
  },
  resultHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[2],
  },
  resultTitle: {
    ...typography.cardTitle,
  },
  resultScore: {
    ...typography.h6,
    color: colors.success,
  },
  resultMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  resultDate: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  resultRank: {
    ...typography.caption,
    color: colors.primary,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  categoryCard: {
    width: '48%',
    marginBottom: spacing[3],
    alignItems: 'center',
    paddingVertical: spacing[4],
  },
  categoryTitle: {
    ...typography.cardTitle,
    marginBottom: spacing[1],
  },
  categoryCount: {
    ...typography.caption,
    color: colors.text.secondary,
  },
});
