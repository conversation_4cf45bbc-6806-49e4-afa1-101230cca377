import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {Card, Input, Button} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';

interface FAQItemProps {
  question: string;
  answer: string;
  isExpanded: boolean;
  onToggle: () => void;
}

const FAQItem: React.FC<FAQItemProps> = ({
  question,
  answer,
  isExpanded,
  onToggle,
}) => (
  <Card style={styles.faqItem}>
    <TouchableOpacity style={styles.faqHeader} onPress={onToggle}>
      <Text style={styles.faqQuestion}>{question}</Text>
      <Icon
        name={isExpanded ? 'chevron-up' : 'chevron-down'}
        size={20}
        color={colors.text.secondary}
      />
    </TouchableOpacity>
    {isExpanded && (
      <View style={styles.faqAnswer}>
        <Text style={styles.faqAnswerText}>{answer}</Text>
      </View>
    )}
  </Card>
);

export const HelpScreen: React.FC = () => {
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);
  const [contactForm, setContactForm] = useState({
    subject: '',
    message: '',
  });

  const faqs = [
    {
      question: 'Uygulamayı nasıl kullanabilirim?',
      answer: 'Uygulamayı kullanmak için önce kayıt olmanız gerekiyor. Kayıt olduktan sonra dersler, quizler ve sosyal özellikler sekmelerinden istediğiniz içeriğe erişebilirsiniz.',
    },
    {
      question: 'Misafir modunda neler yapabilirim?',
      answer: 'Misafir modunda sınırlı içeriğe erişebilirsiniz. Tüm özelliklere erişmek için giriş yapmanız gerekiyor.',
    },
    {
      question: 'Şifremi unuttum, ne yapmalıyım?',
      answer: 'Giriş ekranında "Şifremi Unuttum" butonuna tıklayarak şifre sıfırlama işlemini başlatabilirsiniz.',
    },
    {
      question: 'Quiz sonuçlarım nasıl hesaplanıyor?',
      answer: 'Quiz sonuçlarınız doğru cevap sayınıza göre hesaplanır. Her doğru cevap için puan alırsınız.',
    },
    {
      question: 'İlerleme raporumu nasıl görebilirim?',
      answer: 'Menü sekmesinden "İlerleme Raporu" seçeneğine tıklayarak detaylı ilerleme raporunuzu görebilirsiniz.',
    },
    {
      question: 'Bildirimler nasıl kapatılır?',
      answer: 'Ayarlar sekmesinden bildirim ayarlarınızı değiştirebilirsiniz.',
    },
  ];

  const contactOptions = [
    {
      icon: 'mail-outline',
      title: 'E-posta',
      subtitle: '<EMAIL>',
      onPress: () => Linking.openURL('mailto:<EMAIL>'),
    },
    {
      icon: 'call-outline',
      title: 'Telefon',
      subtitle: '+90 (*************',
      onPress: () => Linking.openURL('tel:+902125550123'),
    },
    {
      icon: 'logo-whatsapp',
      title: 'WhatsApp',
      subtitle: 'Hızlı destek',
      onPress: () => Linking.openURL('https://wa.me/902125550123'),
    },
  ];

  const handleSendMessage = () => {
    if (!contactForm.subject.trim() || !contactForm.message.trim()) {
      return;
    }

    // Here you would typically send the message to your support system
    console.log('Sending message:', contactForm);

    // Reset form
    setContactForm({subject: '', message: ''});
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>

        {/* Quick Help */}
        <Card style={styles.quickHelpCard} variant="elevated">
          <View style={styles.quickHelpHeader}>
            <Icon name="help-circle" size={32} color={colors.primary} />
            <View style={styles.quickHelpText}>
              <Text style={styles.quickHelpTitle}>Hızlı Yardım</Text>
              <Text style={styles.quickHelpSubtitle}>
                Sık sorulan sorular ve destek seçenekleri
              </Text>
            </View>
          </View>
        </Card>

        {/* FAQ Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sık Sorulan Sorular</Text>
          {faqs.map((faq, index) => (
            <FAQItem
              key={index}
              question={faq.question}
              answer={faq.answer}
              isExpanded={expandedFAQ === index}
              onToggle={() => setExpandedFAQ(expandedFAQ === index ? null : index)}
            />
          ))}
        </View>

        {/* Contact Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>İletişim</Text>
          <Card style={styles.contactCard}>
            {contactOptions.map((option, index) => (
              <TouchableOpacity
                key={index}
                style={styles.contactOption}
                onPress={option.onPress}>
                <View style={styles.contactIcon}>
                  <Icon name={option.icon} size={24} color={colors.primary} />
                </View>
                <View style={styles.contactText}>
                  <Text style={styles.contactTitle}>{option.title}</Text>
                  <Text style={styles.contactSubtitle}>{option.subtitle}</Text>
                </View>
                <Icon name="chevron-forward" size={20} color={colors.text.tertiary} />
              </TouchableOpacity>
            ))}
          </Card>
        </View>

        {/* Contact Form */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Bize Yazın</Text>
          <Card style={styles.contactFormCard}>
            <Input
              label="Konu"
              placeholder="Mesajınızın konusu"
              value={contactForm.subject}
              onChangeText={value => setContactForm(prev => ({...prev, subject: value}))}
            />
            <Input
              label="Mesaj"
              placeholder="Mesajınızı buraya yazın..."
              value={contactForm.message}
              onChangeText={value => setContactForm(prev => ({...prev, message: value}))}
              multiline
              numberOfLines={4}
              style={styles.messageInput}
            />
            <Button
              title="Mesaj Gönder"
              onPress={handleSendMessage}
              disabled={!contactForm.subject.trim() || !contactForm.message.trim()}
              fullWidth
            />
          </Card>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[4],
  },
  quickHelpCard: {
    marginBottom: spacing[6],
    padding: spacing[5],
  },
  quickHelpHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quickHelpText: {
    flex: 1,
    marginLeft: spacing[3],
  },
  quickHelpTitle: {
    ...typography.h4,
    marginBottom: spacing[1],
  },
  quickHelpSubtitle: {
    ...typography.body2,
    color: colors.text.secondary,
  },
  section: {
    marginBottom: spacing[6],
  },
  sectionTitle: {
    ...typography.h5,
    marginBottom: spacing[4],
  },
  faqItem: {
    marginBottom: spacing[2],
  },
  faqHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing[2],
  },
  faqQuestion: {
    ...typography.cardTitle,
    flex: 1,
    marginRight: spacing[2],
  },
  faqAnswer: {
    paddingTop: spacing[2],
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  faqAnswerText: {
    ...typography.body2,
    color: colors.text.secondary,
    lineHeight: 22,
  },
  contactCard: {
    padding: 0,
  },
  contactOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing[4],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  contactIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primaryMuted,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing[3],
  },
  contactText: {
    flex: 1,
  },
  contactTitle: {
    ...typography.cardTitle,
    marginBottom: spacing[1],
  },
  contactSubtitle: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  contactFormCard: {
    padding: spacing[4],
  },
  messageInput: {
    minHeight: 100,
    textAlignVertical: 'top',
  },
});
