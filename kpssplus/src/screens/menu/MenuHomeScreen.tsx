import React from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import {StackScreenProps} from '@react-navigation/stack';
import {CompositeScreenProps} from '@react-navigation/native';
import {BottomTabScreenProps} from '@react-navigation/bottom-tabs';
import {MenuStackParamList, MainTabParamList, RootStackParamList} from '../../navigation/types';
import {Card} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
import {useAuth} from '../../hooks';
import Icon from 'react-native-vector-icons/Ionicons';

type Props = CompositeScreenProps<
  StackScreenProps<MenuStackParamList, 'MenuHome'>,
  CompositeScreenProps<
    BottomTabScreenProps<MainTabParamList, 'Menu'>,
    StackScreenProps<RootStackParamList>
  >
>;

interface MenuItemProps {
  icon: string;
  title: string;
  subtitle?: string;
  onPress: () => void;
  showChevron?: boolean;
  badge?: string;
}

const MenuItem: React.FC<MenuItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  showChevron = true,
  badge,
}) => (
  <Card style={styles.menuItem} onPress={onPress} variant="outlined">
    <View style={styles.menuItemContent}>
      <View style={styles.menuItemLeft}>
        <View style={styles.iconContainer}>
          <Icon name={icon} size={24} color={colors.primary} />
        </View>
        <View style={styles.menuItemText}>
          <Text style={styles.menuItemTitle}>{title}</Text>
          {subtitle && <Text style={styles.menuItemSubtitle}>{subtitle}</Text>}
        </View>
      </View>
      <View style={styles.menuItemRight}>
        {badge && (
          <View style={styles.badge}>
            <Text style={styles.badgeText}>{badge}</Text>
          </View>
        )}
        {showChevron && (
          <Icon name="chevron-forward" size={20} color={colors.text.tertiary} />
        )}
      </View>
    </View>
  </Card>
);

export const MenuHomeScreen: React.FC<Props> = ({navigation}) => {
  const {isAuthenticated, user, logout} = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleLogin = () => {
    navigation.navigate('Auth', {
      screen: 'Login',
    });
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* User Profile Section */}
        {isAuthenticated && user ? (
          <Card style={styles.profileCard} variant="elevated">
            <View style={styles.profileContent}>
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>
                  {user.firstName?.charAt(0) || user.username.charAt(0)}
                </Text>
              </View>
              <View style={styles.profileInfo}>
                <Text style={styles.profileName}>
                  {user.firstName && user.lastName
                    ? `${user.firstName} ${user.lastName}`
                    : user.username}
                </Text>
                <Text style={styles.profileEmail}>{user.email}</Text>
              </View>
              <Icon
                name="chevron-forward"
                size={20}
                color={colors.text.tertiary}
              />
            </View>
          </Card>
        ) : (
          <Card style={styles.guestCard} variant="outlined" onPress={handleLogin}>
            <View style={styles.guestContent}>
              <Icon name="person-outline" size={40} color={colors.gray[400]} />
              <View style={styles.guestInfo}>
                <Text style={styles.guestTitle}>Misafir Kullanıcı</Text>
                <Text style={styles.guestSubtitle}>
                  Tüm özelliklere erişmek için giriş yapın
                </Text>
              </View>
              <Icon
                name="chevron-forward"
                size={20}
                color={colors.text.tertiary}
              />
            </View>
          </Card>
        )}

        {/* Account Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Hesap</Text>

          <MenuItem
            icon="person-outline"
            title="Profilim"
            subtitle="Profil bilgilerini düzenle"
            onPress={() => navigation.navigate('Profile')}
          />

          <MenuItem
            icon="settings-outline"
            title="Ayarlar"
            subtitle="Uygulama ayarları"
            onPress={() => navigation.navigate('Settings')}
          />

          <MenuItem
            icon="notifications-outline"
            title="Bildirimler"
            subtitle="Bildirim ayarları"
            onPress={() => navigation.navigate('Notifications')}
            badge="3"
          />
        </View>

        {/* Progress Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>İlerleme</Text>

          <MenuItem
            icon="trending-up-outline"
            title="İstatistikler"
            subtitle="Detaylı performans analizi"
            onPress={() => navigation.navigate('Analytics')}
          />

          <MenuItem
            icon="bar-chart-outline"
            title="İlerleme Raporu"
            subtitle="Çalışma geçmişin"
            onPress={() => navigation.navigate('Progress')}
          />

          <MenuItem
            icon="flag-outline"
            title="Hedeflerim"
            subtitle="Çalışma hedefleri"
            onPress={() => navigation.navigate('Goals')}
          />

          <MenuItem
            icon="medal-outline"
            title="Rozetlerim"
            subtitle="Kazandığın başarı rozetleri"
            onPress={() => navigation.navigate('Badges')}
          />
        </View>

        {/* Support Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Destek</Text>

          <MenuItem
            icon="help-circle-outline"
            title="Yardım"
            subtitle="SSS ve destek"
            onPress={() => navigation.navigate('Help')}
          />

          <MenuItem
            icon="information-circle-outline"
            title="Hakkında"
            subtitle="Uygulama bilgileri"
            onPress={() => navigation.navigate('About')}
          />
        </View>

        {/* Auth Actions */}
        <View style={styles.section}>
          {isAuthenticated ? (
            <MenuItem
              icon="log-out-outline"
              title="Çıkış Yap"
              onPress={handleLogout}
              showChevron={false}
            />
          ) : (
            <MenuItem
              icon="log-in-outline"
              title="Giriş Yap"
              subtitle="Hesabına giriş yap"
              onPress={handleLogin}
            />
          )}
        </View>

        {/* App Version */}
        <View style={styles.versionContainer}>
          <Text style={styles.versionText}>Sürüm 1.0.0</Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: spacing[4],
  },
  profileCard: {
    marginVertical: spacing[4],
  },
  profileContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing[4],
  },
  avatarText: {
    ...typography.h4,
    color: colors.white,
    fontWeight: '600',
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    ...typography.h5,
    marginBottom: spacing[1],
  },
  profileEmail: {
    ...typography.body2,
    color: colors.text.secondary,
  },
  guestCard: {
    marginVertical: spacing[4],
    backgroundColor: colors.gray[50],
  },
  guestContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  guestInfo: {
    flex: 1,
    marginLeft: spacing[4],
  },
  guestTitle: {
    ...typography.h6,
    marginBottom: spacing[1],
  },
  guestSubtitle: {
    ...typography.body2,
    color: colors.text.secondary,
  },
  section: {
    marginVertical: spacing[4],
  },
  sectionTitle: {
    ...typography.h6,
    color: colors.text.secondary,
    marginBottom: spacing[3],
    marginLeft: spacing[2],
  },
  menuItem: {
    marginBottom: spacing[2],
  },
  menuItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primaryMuted,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing[3],
  },
  menuItemText: {
    flex: 1,
  },
  menuItemTitle: {
    ...typography.cardTitle,
    marginBottom: spacing[1],
  },
  menuItemSubtitle: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  menuItemRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  badge: {
    backgroundColor: colors.error,
    borderRadius: 10,
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    marginRight: spacing[2],
    minWidth: 20,
    alignItems: 'center',
  },
  badgeText: {
    ...typography.caption,
    color: colors.white,
    fontSize: 10,
    fontWeight: '600',
  },
  versionContainer: {
    alignItems: 'center',
    paddingVertical: spacing[6],
  },
  versionText: {
    ...typography.caption,
    color: colors.text.tertiary,
  },
});
