import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {Card} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';

export const AboutScreen: React.FC = () => {
  const appInfo = {
    name: 'KPSS Plus',
    version: '1.0.0',
    buildNumber: '2024.01.15',
    description: 'KPSS sınavına hazırlık için kapsamlı eğitim platformu',
  };

  const team = [
    {
      name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      role: 'Mobil Uygulama Geliştirme',
      icon: 'code-outline',
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON> E<PERSON>',
      role: 'Eğitim İçeriği Hazırlama',
      icon: 'book-outline',
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      role: 'UI/UX Tasarım',
      icon: 'color-palette-outline',
    },
  ];

  const links = [
    {
      title: '<PERSON><PERSON><PERSON>lik Politikası',
      icon: 'shield-checkmark-outline',
      onPress: () => Linking.openURL('https://kpssplus.com/privacy'),
    },
    {
      title: 'Kullanım Koşulları',
      icon: 'document-text-outline',
      onPress: () => Linking.openURL('https://kpssplus.com/terms'),
    },
    {
      title: 'Web Sitesi',
      icon: 'globe-outline',
      onPress: () => Linking.openURL('https://kpssplus.com'),
    },
    {
      title: 'Sosyal Medya',
      icon: 'logo-instagram',
      onPress: () => Linking.openURL('https://instagram.com/kpssplus'),
    },
  ];

  const features = [
    'Kapsamlı KPSS ders içerikleri',
    'İnteraktif quiz sistemi',
    'İlerleme takibi ve analitik',
    'Sosyal öğrenme platformu',
    'Offline içerik desteği',
    'Kişiselleştirilmiş öğrenme',
  ];

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>

        {/* App Header */}
        <Card style={styles.headerCard} variant="elevated">
          <View style={styles.appIcon}>
            <Icon name="school" size={48} color={colors.white} />
          </View>
          <Text style={styles.appName}>{appInfo.name}</Text>
          <Text style={styles.appDescription}>{appInfo.description}</Text>
          <View style={styles.versionInfo}>
            <Text style={styles.versionText}>Sürüm {appInfo.version}</Text>
            <Text style={styles.buildText}>Build {appInfo.buildNumber}</Text>
          </View>
        </Card>

        {/* Features */}
        <Card style={styles.featuresCard}>
          <Text style={styles.sectionTitle}>Özellikler</Text>
          {features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <Icon name="checkmark-circle" size={20} color={colors.success} />
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
        </Card>

        {/* Team */}
        <Card style={styles.teamCard}>
          <Text style={styles.sectionTitle}>Ekibimiz</Text>
          {team.map((member, index) => (
            <View key={index} style={styles.teamMember}>
              <View style={styles.teamIcon}>
                <Icon name={member.icon} size={24} color={colors.primary} />
              </View>
              <View style={styles.teamInfo}>
                <Text style={styles.teamName}>{member.name}</Text>
                <Text style={styles.teamRole}>{member.role}</Text>
              </View>
            </View>
          ))}
        </Card>

        {/* Links */}
        <Card style={styles.linksCard}>
          <Text style={styles.sectionTitle}>Bağlantılar</Text>
          {links.map((link, index) => (
            <TouchableOpacity
              key={index}
              style={styles.linkItem}
              onPress={link.onPress}>
              <View style={styles.linkIcon}>
                <Icon name={link.icon} size={24} color={colors.primary} />
              </View>
              <Text style={styles.linkText}>{link.title}</Text>
              <Icon name="chevron-forward" size={20} color={colors.text.tertiary} />
            </TouchableOpacity>
          ))}
        </Card>

        {/* Copyright */}
        <View style={styles.copyright}>
          <Text style={styles.copyrightText}>
            © 2024 KPSS Plus. Tüm hakları saklıdır.
          </Text>
          <Text style={styles.copyrightSubtext}>
            Bu uygulama eğitim amaçlı geliştirilmiştir.
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[4],
  },
  headerCard: {
    alignItems: 'center',
    padding: spacing[6],
    marginBottom: spacing[6],
  },
  appIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing[4],
  },
  appName: {
    ...typography.h2,
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  appDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: spacing[4],
  },
  versionInfo: {
    alignItems: 'center',
  },
  versionText: {
    ...typography.body2,
    fontWeight: '600',
    marginBottom: spacing[1],
  },
  buildText: {
    ...typography.caption,
    color: colors.text.tertiary,
  },
  sectionTitle: {
    ...typography.h5,
    marginBottom: spacing[4],
  },
  featuresCard: {
    marginBottom: spacing[4],
    padding: spacing[4],
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing[2],
  },
  featureText: {
    ...typography.body2,
    marginLeft: spacing[3],
    flex: 1,
  },
  teamCard: {
    marginBottom: spacing[4],
    padding: spacing[4],
  },
  teamMember: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  teamIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primaryMuted,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing[3],
  },
  teamInfo: {
    flex: 1,
  },
  teamName: {
    ...typography.cardTitle,
    marginBottom: spacing[1],
  },
  teamRole: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  linksCard: {
    marginBottom: spacing[6],
    padding: 0,
  },
  linkItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: spacing[4],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  linkIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primaryMuted,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing[3],
  },
  linkText: {
    ...typography.cardTitle,
    flex: 1,
  },
  copyright: {
    alignItems: 'center',
    paddingVertical: spacing[6],
  },
  copyrightText: {
    ...typography.body2,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing[2],
  },
  copyrightSubtext: {
    ...typography.caption,
    color: colors.text.tertiary,
    textAlign: 'center',
  },
});
