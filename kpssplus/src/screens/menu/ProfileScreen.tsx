import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {Button, Input, Card} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
import {useAuth} from '../../hooks';

export const ProfileScreen: React.FC = () => {
  const {user, isAuthenticated, refreshUser} = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    username: user?.username || '',
    email: user?.email || '',
  });

  const handleSave = async () => {
    try {
      // Here you would typically call an API to update user profile
      Alert.alert('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 'Profil bilgileri gü<PERSON>llen<PERSON>', [
        {text: 'Tamam', onPress: () => setIsEditing(false)},
      ]);
      await refreshUser();
    } catch (error) {
      Alert.alert('Hata', 'Profil güncellenirken bir hata oluştu');
    }
  };

  const handleCancel = () => {
    setEditForm({
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      username: user?.username || '',
      email: user?.email || '',
    });
    setIsEditing(false);
  };

  if (!isAuthenticated || !user) {
    return (
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <View style={styles.emptyState}>
          <Icon name="person-outline" size={80} color={colors.gray[400]} />
          <Text style={styles.emptyTitle}>Giriş Yapın</Text>
          <Text style={styles.emptyDescription}>
            Profil bilgilerinizi görmek için giriş yapmanız gerekiyor
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>
        {/* Profile Header */}
        <Card style={styles.profileHeader} variant="elevated">
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>
                {user.firstName?.charAt(0) || user.username.charAt(0)}
              </Text>
            </View>
            <TouchableOpacity style={styles.avatarEditButton}>
              <Icon name="camera-outline" size={20} color={colors.white} />
            </TouchableOpacity>
          </View>
          <Text style={styles.profileName}>
            {user.firstName && user.lastName
              ? `${user.firstName} ${user.lastName}`
              : user.username}
          </Text>
          <Text style={styles.profileEmail}>{user.email}</Text>
        </Card>

        {/* Profile Form */}
        <Card style={styles.formCard}>
          <View style={styles.formHeader}>
            <Text style={styles.formTitle}>Profil Bilgileri</Text>
            {!isEditing ? (
              <TouchableOpacity
                style={styles.editButton}
                onPress={() => setIsEditing(true)}>
                <Icon name="pencil-outline" size={20} color={colors.primary} />
                <Text style={styles.editButtonText}>Düzenle</Text>
              </TouchableOpacity>
            ) : (
              <View style={styles.editActions}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={handleCancel}>
                  <Text style={styles.cancelButtonText}>İptal</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.saveButton}
                  onPress={handleSave}>
                  <Text style={styles.saveButtonText}>Kaydet</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>

          <View style={styles.form}>
            <Input
              label="Ad"
              value={isEditing ? editForm.firstName : user.firstName || ''}
              onChangeText={value =>
                setEditForm(prev => ({...prev, firstName: value}))
              }
              editable={isEditing}
              leftIcon="person-outline"
            />

            <Input
              label="Soyad"
              value={isEditing ? editForm.lastName : user.lastName || ''}
              onChangeText={value =>
                setEditForm(prev => ({...prev, lastName: value}))
              }
              editable={isEditing}
              leftIcon="person-outline"
            />

            <Input
              label="Kullanıcı Adı"
              value={isEditing ? editForm.username : user.username}
              onChangeText={value =>
                setEditForm(prev => ({...prev, username: value}))
              }
              editable={isEditing}
              leftIcon="at-outline"
            />

            <Input
              label="E-posta"
              value={isEditing ? editForm.email : user.email}
              onChangeText={value =>
                setEditForm(prev => ({...prev, email: value}))
              }
              editable={isEditing}
              leftIcon="mail-outline"
              keyboardType="email-address"
            />
          </View>
        </Card>

        {/* Account Actions */}
        <Card style={styles.actionsCard}>
          <Text style={styles.actionsTitle}>Hesap İşlemleri</Text>

          <TouchableOpacity style={styles.actionItem}>
            <Icon name="key-outline" size={24} color={colors.primary} />
            <Text style={styles.actionText}>Şifre Değiştir</Text>
            <Icon name="chevron-forward" size={20} color={colors.text.tertiary} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionItem}>
            <Icon name="shield-checkmark-outline" size={24} color={colors.primary} />
            <Text style={styles.actionText}>Güvenlik Ayarları</Text>
            <Icon name="chevron-forward" size={20} color={colors.text.tertiary} />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionItem}>
            <Icon name="download-outline" size={24} color={colors.primary} />
            <Text style={styles.actionText}>Verilerimi İndir</Text>
            <Icon name="chevron-forward" size={20} color={colors.text.tertiary} />
          </TouchableOpacity>

          <TouchableOpacity style={[styles.actionItem, styles.dangerAction]}>
            <Icon name="trash-outline" size={24} color={colors.error} />
            <Text style={[styles.actionText, styles.dangerText]}>Hesabı Sil</Text>
            <Icon name="chevron-forward" size={20} color={colors.text.tertiary} />
          </TouchableOpacity>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[4],
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
  },
  emptyTitle: {
    ...typography.h4,
    marginTop: spacing[4],
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  emptyDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  profileHeader: {
    alignItems: 'center',
    paddingVertical: spacing[6],
    marginBottom: spacing[4],
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: spacing[4],
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    ...typography.h2,
    color: colors.white,
    fontWeight: '600',
  },
  avatarEditButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: colors.white,
  },
  profileName: {
    ...typography.h4,
    marginBottom: spacing[1],
    textAlign: 'center',
  },
  profileEmail: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
  },
  formCard: {
    marginBottom: spacing[4],
  },
  formHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[4],
  },
  formTitle: {
    ...typography.h5,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    borderRadius: 8,
    backgroundColor: colors.primaryMuted,
  },
  editButtonText: {
    ...typography.button,
    color: colors.primary,
    marginLeft: spacing[1],
  },
  editActions: {
    flexDirection: 'row',
    gap: spacing[2],
  },
  cancelButton: {
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    borderRadius: 8,
    backgroundColor: colors.gray[100],
  },
  cancelButtonText: {
    ...typography.button,
    color: colors.text.secondary,
  },
  saveButton: {
    paddingHorizontal: spacing[3],
    paddingVertical: spacing[2],
    borderRadius: 8,
    backgroundColor: colors.primary,
  },
  saveButtonText: {
    ...typography.button,
    color: colors.white,
  },
  form: {
    gap: spacing[2],
  },
  actionsCard: {
    marginBottom: spacing[4],
  },
  actionsTitle: {
    ...typography.h5,
    marginBottom: spacing[4],
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  actionText: {
    ...typography.body1,
    flex: 1,
    marginLeft: spacing[3],
  },
  dangerAction: {
    borderBottomWidth: 0,
  },
  dangerText: {
    color: colors.error,
  },
});
