import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {Card} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';

interface SettingItemProps {
  icon: string;
  title: string;
  subtitle?: string;
  onPress?: () => void;
  rightElement?: React.ReactNode;
  showChevron?: boolean;
}

const SettingItem: React.FC<SettingItemProps> = ({
  icon,
  title,
  subtitle,
  onPress,
  rightElement,
  showChevron = true,
}) => (
  <TouchableOpacity
    style={styles.settingItem}
    onPress={onPress}
    disabled={!onPress}>
    <View style={styles.settingLeft}>
      <View style={styles.iconContainer}>
        <Icon name={icon} size={24} color={colors.primary} />
      </View>
      <View style={styles.settingText}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
    </View>
    <View style={styles.settingRight}>
      {rightElement}
      {showChevron && onPress && (
        <Icon name="chevron-forward" size={20} color={colors.text.tertiary} />
      )}
    </View>
  </TouchableOpacity>
);

export const SettingsScreen: React.FC = () => {
  const [settings, setSettings] = useState({
    notifications: true,
    darkMode: false,
    autoPlay: true,
    downloadOnWifi: true,
    analytics: true,
    crashReports: true,
  });

  const toggleSetting = (key: keyof typeof settings) => {
    setSettings(prev => ({...prev, [key]: !prev[key]}));
  };

  const handleLanguagePress = () => {
    Alert.alert('Dil Seçimi', 'Dil seçimi özelliği yakında eklenecek');
  };

  const handleThemePress = () => {
    Alert.alert('Tema Seçimi', 'Tema seçimi özelliği yakında eklenecek');
  };

  const handleClearCache = () => {
    Alert.alert(
      'Önbelleği Temizle',
      'Tüm önbellek verileri silinecek. Devam etmek istiyor musunuz?',
      [
        {text: 'İptal', style: 'cancel'},
        {
          text: 'Temizle',
          style: 'destructive',
          onPress: () => {
            Alert.alert('Başarılı', 'Önbellek temizlendi');
          },
        },
      ]
    );
  };

  const handleResetSettings = () => {
    Alert.alert(
      'Ayarları Sıfırla',
      'Tüm ayarlar varsayılan değerlere döndürülecek. Bu işlem geri alınamaz.',
      [
        {text: 'İptal', style: 'cancel'},
        {
          text: 'Sıfırla',
          style: 'destructive',
          onPress: () => {
            setSettings({
              notifications: true,
              darkMode: false,
              autoPlay: true,
              downloadOnWifi: true,
              analytics: true,
              crashReports: true,
            });
            Alert.alert('Başarılı', 'Ayarlar sıfırlandı');
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>

        {/* General Settings */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Genel</Text>

          <SettingItem
            icon="language-outline"
            title="Dil"
            subtitle="Türkçe"
            onPress={handleLanguagePress}
          />

          <SettingItem
            icon="color-palette-outline"
            title="Tema"
            subtitle="Açık tema"
            onPress={handleThemePress}
          />

          <SettingItem
            icon="notifications-outline"
            title="Bildirimler"
            subtitle="Push bildirimleri"
            rightElement={
              <Switch
                value={settings.notifications}
                onValueChange={() => toggleSetting('notifications')}
                trackColor={{false: colors.gray[300], true: colors.primaryMuted}}
                thumbColor={settings.notifications ? colors.primary : colors.gray[500]}
              />
            }
            showChevron={false}
          />
        </Card>

        {/* Content Settings */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>İçerik</Text>

          <SettingItem
            icon="play-outline"
            title="Otomatik Oynat"
            subtitle="Videoları otomatik başlat"
            rightElement={
              <Switch
                value={settings.autoPlay}
                onValueChange={() => toggleSetting('autoPlay')}
                trackColor={{false: colors.gray[300], true: colors.primaryMuted}}
                thumbColor={settings.autoPlay ? colors.primary : colors.gray[500]}
              />
            }
            showChevron={false}
          />

          <SettingItem
            icon="wifi-outline"
            title="Sadece WiFi'da İndir"
            subtitle="Mobil veri kullanımını sınırla"
            rightElement={
              <Switch
                value={settings.downloadOnWifi}
                onValueChange={() => toggleSetting('downloadOnWifi')}
                trackColor={{false: colors.gray[300], true: colors.primaryMuted}}
                thumbColor={settings.downloadOnWifi ? colors.primary : colors.gray[500]}
              />
            }
            showChevron={false}
          />
        </Card>

        {/* Privacy Settings */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Gizlilik</Text>

          <SettingItem
            icon="analytics-outline"
            title="Kullanım Analizi"
            subtitle="Anonim kullanım verilerini paylaş"
            rightElement={
              <Switch
                value={settings.analytics}
                onValueChange={() => toggleSetting('analytics')}
                trackColor={{false: colors.gray[300], true: colors.primaryMuted}}
                thumbColor={settings.analytics ? colors.primary : colors.gray[500]}
              />
            }
            showChevron={false}
          />

          <SettingItem
            icon="bug-outline"
            title="Hata Raporları"
            subtitle="Otomatik hata raporlaması"
            rightElement={
              <Switch
                value={settings.crashReports}
                onValueChange={() => toggleSetting('crashReports')}
                trackColor={{false: colors.gray[300], true: colors.primaryMuted}}
                thumbColor={settings.crashReports ? colors.primary : colors.gray[500]}
              />
            }
            showChevron={false}
          />
        </Card>

        {/* Storage & Data */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Depolama ve Veri</Text>

          <SettingItem
            icon="trash-outline"
            title="Önbelleği Temizle"
            subtitle="Geçici dosyaları sil"
            onPress={handleClearCache}
          />

          <SettingItem
            icon="refresh-outline"
            title="Ayarları Sıfırla"
            subtitle="Tüm ayarları varsayılana döndür"
            onPress={handleResetSettings}
          />
        </Card>

        {/* App Info */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Uygulama</Text>

          <SettingItem
            icon="information-circle-outline"
            title="Sürüm"
            subtitle="1.0.0"
            showChevron={false}
          />

          <SettingItem
            icon="build-outline"
            title="Build"
            subtitle="2024.01.15"
            showChevron={false}
          />
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[4],
  },
  section: {
    marginBottom: spacing[4],
  },
  sectionTitle: {
    ...typography.h6,
    color: colors.text.secondary,
    marginBottom: spacing[3],
    marginLeft: spacing[2],
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primaryMuted,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing[3],
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    ...typography.cardTitle,
    marginBottom: spacing[1],
  },
  settingSubtitle: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing[2],
  },
});
