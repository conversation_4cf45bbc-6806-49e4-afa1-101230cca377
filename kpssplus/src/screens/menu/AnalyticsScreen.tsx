import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/Ionicons';
import {Card} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
import {useAuth} from '../../hooks';

const {width} = Dimensions.get('window');

interface StatCardProps {
  icon: string;
  title: string;
  value: string;
  subtitle?: string;
  color?: string;
  trend?: 'up' | 'down' | 'neutral';
  trendValue?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  icon,
  title,
  value,
  subtitle,
  color = colors.primary,
  trend,
  trendValue,
}) => (
  <Card style={styles.statCard} variant="elevated">
    <View style={styles.statHeader}>
      <View style={[styles.statIcon, {backgroundColor: `${color}20`}]}>
        <Icon name={icon} size={24} color={color} />
      </View>
      {trend && trendValue && (
        <View style={styles.trendContainer}>
          <Icon
            name={
              trend === 'up'
                ? 'trending-up'
                : trend === 'down'
                ? 'trending-down'
                : 'remove'
            }
            size={16}
            color={
              trend === 'up'
                ? colors.success
                : trend === 'down'
                ? colors.error
                : colors.gray[500]
            }
          />
          <Text
            style={[
              styles.trendText,
              {
                color:
                  trend === 'up'
                    ? colors.success
                    : trend === 'down'
                    ? colors.error
                    : colors.gray[500],
              },
            ]}>
            {trendValue}
          </Text>
        </View>
      )}
    </View>
    <Text style={styles.statValue}>{value}</Text>
    <Text style={styles.statTitle}>{title}</Text>
    {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
  </Card>
);

interface TimeFilterProps {
  selected: string;
  onSelect: (period: string) => void;
}

const TimeFilter: React.FC<TimeFilterProps> = ({selected, onSelect}) => {
  const periods = [
    {key: 'week', label: 'Bu Hafta'},
    {key: 'month', label: 'Bu Ay'},
    {key: 'year', label: 'Bu Yıl'},
    {key: 'all', label: 'Tümü'},
  ];

  return (
    <View style={styles.timeFilter}>
      {periods.map(period => (
        <TouchableOpacity
          key={period.key}
          style={[
            styles.timeFilterButton,
            selected === period.key && styles.timeFilterButtonActive,
          ]}
          onPress={() => onSelect(period.key)}>
          <Text
            style={[
              styles.timeFilterText,
              selected === period.key && styles.timeFilterTextActive,
            ]}>
            {period.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export const AnalyticsScreen: React.FC = () => {
  const {isAuthenticated} = useAuth();
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  // Mock data - in real app, this would come from API
  const stats = {
    studyTime: {
      value: '24.5',
      unit: 'saat',
      trend: 'up' as const,
      trendValue: '+12%',
    },
    completedLessons: {
      value: '47',
      unit: 'ders',
      trend: 'up' as const,
      trendValue: '+8',
    },
    quizScore: {
      value: '85',
      unit: '%',
      trend: 'up' as const,
      trendValue: '+5%',
    },
    streak: {
      value: '12',
      unit: 'gün',
      trend: 'neutral' as const,
      trendValue: '0',
    },
  };

  const achievements = [
    {
      icon: 'flame-outline',
      title: 'Çalışma Serisi',
      description: '12 gün üst üste çalıştın!',
      color: colors.warning,
    },
    {
      icon: 'trophy-outline',
      title: 'Quiz Ustası',
      description: '10 quiz\'i %90+ ile tamamladın',
      color: colors.success,
    },
    {
      icon: 'time-outline',
      title: 'Zaman Yöneticisi',
      description: 'Bu ay 25+ saat çalıştın',
      color: colors.primary,
    },
  ];

  if (!isAuthenticated) {
    return (
      <SafeAreaView style={styles.container} edges={['bottom']}>
        <View style={styles.emptyState}>
          <Icon name="analytics-outline" size={80} color={colors.gray[400]} />
          <Text style={styles.emptyTitle}>Giriş Yapın</Text>
          <Text style={styles.emptyDescription}>
            İstatistiklerinizi görmek için giriş yapmanız gerekiyor
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container} edges={['bottom']}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}>

        {/* Time Filter */}
        <TimeFilter selected={selectedPeriod} onSelect={setSelectedPeriod} />

        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          <StatCard
            icon="time-outline"
            title="Çalışma Süresi"
            value={stats.studyTime.value}
            subtitle={stats.studyTime.unit}
            color={colors.primary}
            trend={stats.studyTime.trend}
            trendValue={stats.studyTime.trendValue}
          />
          <StatCard
            icon="book-outline"
            title="Tamamlanan Ders"
            value={stats.completedLessons.value}
            subtitle={stats.completedLessons.unit}
            color={colors.success}
            trend={stats.completedLessons.trend}
            trendValue={stats.completedLessons.trendValue}
          />
          <StatCard
            icon="checkmark-circle-outline"
            title="Quiz Başarısı"
            value={stats.quizScore.value}
            subtitle={stats.quizScore.unit}
            color={colors.warning}
            trend={stats.quizScore.trend}
            trendValue={stats.quizScore.trendValue}
          />
          <StatCard
            icon="flame-outline"
            title="Çalışma Serisi"
            value={stats.streak.value}
            subtitle={stats.streak.unit}
            color={colors.error}
            trend={stats.streak.trend}
            trendValue={stats.streak.trendValue}
          />
        </View>

        {/* Recent Achievements */}
        <Card style={styles.achievementsCard}>
          <Text style={styles.sectionTitle}>Son Başarılar</Text>
          {achievements.map((achievement, index) => (
            <View key={index} style={styles.achievementItem}>
              <View style={[styles.achievementIcon, {backgroundColor: `${achievement.color}20`}]}>
                <Icon name={achievement.icon} size={24} color={achievement.color} />
              </View>
              <View style={styles.achievementText}>
                <Text style={styles.achievementTitle}>{achievement.title}</Text>
                <Text style={styles.achievementDescription}>{achievement.description}</Text>
              </View>
            </View>
          ))}
        </Card>

        {/* Study Pattern */}
        <Card style={styles.patternCard}>
          <Text style={styles.sectionTitle}>Çalışma Düzeni</Text>
          <View style={styles.patternGrid}>
            {Array.from({length: 7}, (_, i) => {
              const day = ['Pzt', 'Sal', 'Çar', 'Per', 'Cum', 'Cmt', 'Paz'][i];
              const intensity = Math.random();
              return (
                <View key={i} style={styles.patternDay}>
                  <View
                    style={[
                      styles.patternBar,
                      {
                        height: Math.max(20, intensity * 60),
                        backgroundColor:
                          intensity > 0.7
                            ? colors.success
                            : intensity > 0.4
                            ? colors.warning
                            : colors.gray[300],
                      },
                    ]}
                  />
                  <Text style={styles.patternDayText}>{day}</Text>
                </View>
              );
            })}
          </View>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[4],
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing[4],
  },
  emptyTitle: {
    ...typography.h4,
    marginTop: spacing[4],
    marginBottom: spacing[2],
    textAlign: 'center',
  },
  emptyDescription: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  timeFilter: {
    flexDirection: 'row',
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    padding: spacing[1],
    marginBottom: spacing[6],
  },
  timeFilterButton: {
    flex: 1,
    paddingVertical: spacing[2],
    paddingHorizontal: spacing[3],
    borderRadius: 8,
    alignItems: 'center',
  },
  timeFilterButtonActive: {
    backgroundColor: colors.white,
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  timeFilterText: {
    ...typography.button,
    color: colors.text.secondary,
    fontSize: 14,
  },
  timeFilterTextActive: {
    color: colors.primary,
    fontWeight: '600',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: spacing[6],
  },
  statCard: {
    width: (width - spacing[4] * 2 - spacing[3]) / 2,
    marginBottom: spacing[3],
    padding: spacing[4],
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing[3],
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  trendText: {
    ...typography.caption,
    marginLeft: spacing[1],
    fontWeight: '600',
  },
  statValue: {
    ...typography.h3,
    marginBottom: spacing[1],
  },
  statTitle: {
    ...typography.body2,
    color: colors.text.secondary,
  },
  statSubtitle: {
    ...typography.caption,
    color: colors.text.tertiary,
  },
  sectionTitle: {
    ...typography.h5,
    marginBottom: spacing[4],
  },
  achievementsCard: {
    marginBottom: spacing[6],
  },
  achievementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing[3],
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  achievementIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing[3],
  },
  achievementText: {
    flex: 1,
  },
  achievementTitle: {
    ...typography.cardTitle,
    marginBottom: spacing[1],
  },
  achievementDescription: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  patternCard: {
    marginBottom: spacing[4],
  },
  patternGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 100,
  },
  patternDay: {
    alignItems: 'center',
    flex: 1,
  },
  patternBar: {
    width: 20,
    borderRadius: 10,
    marginBottom: spacing[2],
  },
  patternDayText: {
    ...typography.caption,
    color: colors.text.secondary,
  },
});
