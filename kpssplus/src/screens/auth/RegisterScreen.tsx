import React, {useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {StackScreenProps} from '@react-navigation/stack';
import {AuthStackParamList} from '../../navigation/types';
import {Button, Input} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
import {useAuth} from '../../hooks';

type Props = StackScreenProps<AuthStackParamList, 'Register'>;

interface RegisterForm {
  firstName: string;
  lastName: string;
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

interface RegisterErrors {
  firstName?: string;
  lastName?: string;
  username?: string;
  email?: string;
  password?: string;
  confirmPassword?: string;
}

export const RegisterScreen: React.FC<Props> = ({navigation}) => {
  const {register, isLoading, error, clearError} = useAuth();
  const [form, setForm] = useState<RegisterForm>({
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<RegisterErrors>({});

  const validateForm = (): boolean => {
    const newErrors: RegisterErrors = {};

    // First name validation
    if (!form.firstName.trim()) {
      newErrors.firstName = 'Ad gerekli';
    } else if (form.firstName.trim().length < 2) {
      newErrors.firstName = 'Ad en az 2 karakter olmalı';
    }

    // Last name validation
    if (!form.lastName.trim()) {
      newErrors.lastName = 'Soyad gerekli';
    } else if (form.lastName.trim().length < 2) {
      newErrors.lastName = 'Soyad en az 2 karakter olmalı';
    }

    // Username validation
    if (!form.username.trim()) {
      newErrors.username = 'Kullanıcı adı gerekli';
    } else if (form.username.trim().length < 3) {
      newErrors.username = 'Kullanıcı adı en az 3 karakter olmalı';
    } else if (!/^[a-zA-Z0-9_]+$/.test(form.username.trim())) {
      newErrors.username = 'Kullanıcı adı sadece harf, rakam ve _ içerebilir';
    }

    // Email validation
    if (!form.email.trim()) {
      newErrors.email = 'E-posta adresi gerekli';
    } else if (!/\S+@\S+\.\S+/.test(form.email)) {
      newErrors.email = 'Geçerli bir e-posta adresi girin';
    }

    // Password validation
    if (!form.password) {
      newErrors.password = 'Şifre gerekli';
    } else if (form.password.length < 8) {
      newErrors.password = 'Şifre en az 8 karakter olmalı';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(form.password)) {
      newErrors.password = 'Şifre en az 1 küçük harf, 1 büyük harf ve 1 rakam içermeli';
    }

    // Confirm password validation
    if (!form.confirmPassword) {
      newErrors.confirmPassword = 'Şifre tekrarı gerekli';
    } else if (form.password !== form.confirmPassword) {
      newErrors.confirmPassword = 'Şifreler eşleşmiyor';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleRegister = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      clearError();
      await register({
        firstName: form.firstName.trim(),
        lastName: form.lastName.trim(),
        username: form.username.trim().toLowerCase(),
        email: form.email.trim().toLowerCase(),
        password: form.password,
      });

      // Navigation will be handled by RootNavigator based on auth state
      navigation.goBack();
    } catch (err: any) {
      Alert.alert(
        'Kayıt Hatası',
        err.message || 'Kayıt olurken bir hata oluştu',
        [{text: 'Tamam'}]
      );
    }
  };

  const updateForm = (field: keyof RegisterForm, value: string) => {
    setForm(prev => ({...prev, [field]: value}));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({...prev, [field]: undefined}));
    }
  };

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoid}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}>
          <View style={styles.header}>
            <Text style={styles.title}>Hesap Oluştur</Text>
            <Text style={styles.subtitle}>
              Ücretsiz hesabını oluştur ve öğrenmeye başla
            </Text>
          </View>

          <View style={styles.form}>
            <View style={styles.nameRow}>
              <Input
                label="Ad"
                placeholder="Adınız"
                value={form.firstName}
                onChangeText={value => updateForm('firstName', value)}
                error={errors.firstName}
                leftIcon="person-outline"
                containerStyle={styles.nameInput}
                autoCapitalize="words"
                textContentType="givenName"
              />
              <Input
                label="Soyad"
                placeholder="Soyadınız"
                value={form.lastName}
                onChangeText={value => updateForm('lastName', value)}
                error={errors.lastName}
                leftIcon="person-outline"
                containerStyle={styles.nameInput}
                autoCapitalize="words"
                textContentType="familyName"
              />
            </View>

            <Input
              label="Kullanıcı Adı"
              placeholder="Kullanıcı adınız"
              value={form.username}
              onChangeText={value => updateForm('username', value)}
              error={errors.username}
              leftIcon="at-outline"
              autoCapitalize="none"
              autoCorrect={false}
              textContentType="username"
            />

            <Input
              label="E-posta"
              placeholder="E-posta adresiniz"
              value={form.email}
              onChangeText={value => updateForm('email', value)}
              error={errors.email}
              leftIcon="mail-outline"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              textContentType="emailAddress"
            />

            <Input
              label="Şifre"
              placeholder="Şifreniz"
              value={form.password}
              onChangeText={value => updateForm('password', value)}
              error={errors.password}
              leftIcon="lock-closed-outline"
              secureTextEntry
              textContentType="newPassword"
            />

            <Input
              label="Şifre Tekrarı"
              placeholder="Şifrenizi tekrar girin"
              value={form.confirmPassword}
              onChangeText={value => updateForm('confirmPassword', value)}
              error={errors.confirmPassword}
              leftIcon="lock-closed-outline"
              secureTextEntry
              textContentType="newPassword"
            />

            <Button
              title="Kayıt Ol"
              onPress={handleRegister}
              loading={isLoading}
              disabled={isLoading}
              fullWidth
              style={styles.registerButton}
            />
          </View>

          <View style={styles.footer}>
            <Text style={styles.footerText}>Zaten hesabın var mı?</Text>
            <Button
              title="Giriş Yap"
              onPress={() => navigation.navigate('Login')}
              variant="ghost"
              size="small"
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoid: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: spacing[4],
    paddingVertical: spacing[6],
  },
  header: {
    alignItems: 'center',
    marginBottom: spacing[8],
  },
  title: {
    ...typography.h2,
    marginBottom: spacing[2],
    textAlign: 'center',
    color: colors.text.primary,
  },
  subtitle: {
    ...typography.body1,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  form: {
    flex: 1,
    marginBottom: spacing[6],
  },
  nameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  nameInput: {
    flex: 1,
    marginHorizontal: spacing[1],
  },
  registerButton: {
    marginTop: spacing[4],
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: spacing[4],
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  footerText: {
    ...typography.body2,
    color: colors.text.secondary,
    marginRight: spacing[2],
  },
});
