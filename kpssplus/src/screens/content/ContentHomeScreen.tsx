import React from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import {StackScreenProps} from '@react-navigation/stack';
import {CompositeScreenProps} from '@react-navigation/native';
import {BottomTabScreenProps} from '@react-navigation/bottom-tabs';
import {ContentStackParamList, MainTabParamList, RootStackParamList} from '../../navigation/types';
import {Button, Card} from '../../components/ui';
import {colors, typography, spacing} from '../../theme';
import {useAuth, useGuestMode} from '../../hooks';

type Props = CompositeScreenProps<
  StackScreenProps<ContentStackParamList, 'ContentHome'>,
  CompositeScreenProps<
    BottomTabScreenProps<MainTabParamList, 'Content'>,
    StackScreenProps<RootStackParamList>
  >
>;

export const ContentHomeScreen: React.FC<Props> = ({navigation}) => {
  const {isAuthenticated, isGuest} = useAuth();
  const {getRemainingContentViews, checkContentLimit} = useGuestMode();

  const handleContentPress = (contentId: string) => {
    if (isGuest && checkContentLimit()) {
      // Show guest limit modal
      navigation.navigate('ContentDetail', {contentId});
    } else {
      navigation.navigate('ContentDetail', {contentId});
    }
  };

  const handleSearchPress = () => {
    navigation.navigate('ContentSearch', {});
  };

  const handleLibraryPress = () => {
    if (isAuthenticated) {
      navigation.navigate('ContentLibrary');
    } else {
      // Show login modal or redirect to auth
      console.log('Login required for library');
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}>
        {/* Guest Mode Banner */}
        {isGuest && (
          <Card style={styles.guestBanner} variant="outlined">
            <Text style={styles.guestTitle}>Misafir Modundasınız</Text>
            <Text style={styles.guestText}>
              {checkContentLimit()
                ? 'İçerik görüntüleme limitiniz doldu. Tüm içeriklere erişmek için giriş yapın.'
                : `Kalan içerik görüntüleme hakkınız: ${getRemainingContentViews()}`}
            </Text>
            <Button
              title="Giriş Yap"
              onPress={() => {
                navigation.navigate('Auth', {
                  screen: 'Login',
                });
              }}
              size="small"
              style={styles.loginButton}
            />
          </Card>
        )}

        {/* Search Bar */}
        <Card style={styles.searchCard} onPress={handleSearchPress}>
          <Text style={styles.searchText}>Ders ara...</Text>
        </Card>

        {/* Quick Actions */}
        <View style={styles.quickActions}>
          <Button
            title="Kütüphanem"
            onPress={handleLibraryPress}
            variant="outline"
            style={styles.actionButton}
          />
          <Button
            title="Konular"
            onPress={() => navigation.navigate('TopicList', {})}
            variant="outline"
            style={styles.actionButton}
          />
        </View>

        {/* Popular Content Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Popüler Dersler</Text>

          {/* Mock content cards */}
          {[1, 2, 3, 4, 5].map(item => (
            <Card
              key={item}
              style={styles.contentCard}
              variant="elevated"
              onPress={() => handleContentPress(`content-${item}`)}>
              <View style={styles.contentHeader}>
                <Text style={styles.contentTitle}>Matematik Dersi {item}</Text>
                <Text style={styles.contentSubject}>Matematik</Text>
              </View>
              <Text style={styles.contentDescription}>
                Bu derste matematik konularını detaylı bir şekilde işleyeceğiz.
              </Text>
              <View style={styles.contentFooter}>
                <Text style={styles.contentDuration}>45 dk</Text>
                <Text style={styles.contentDifficulty}>Orta</Text>
              </View>
            </Card>
          ))}
        </View>

        {/* Recent Content Section */}
        {isAuthenticated && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Son İzlenenler</Text>

            {[1, 2, 3].map(item => (
              <Card
                key={item}
                style={styles.contentCard}
                variant="elevated"
                onPress={() => handleContentPress(`recent-${item}`)}>
                <View style={styles.contentHeader}>
                  <Text style={styles.contentTitle}>Fizik Dersi {item}</Text>
                  <Text style={styles.contentSubject}>Fizik</Text>
                </View>
                <View style={styles.progressContainer}>
                  <View style={styles.progressBar}>
                    <View
                      style={[styles.progressFill, {width: `${item * 30}%`}]}
                    />
                  </View>
                  <Text style={styles.progressText}>{item * 30}%</Text>
                </View>
              </Card>
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: spacing[4],
  },
  guestBanner: {
    marginVertical: spacing[4],
    backgroundColor: colors.warning.warningMuted,
    borderColor: colors.warning.warning,
  },
  guestTitle: {
    ...typography.h6,
    color: colors.warning.warningDark,
    marginBottom: spacing[2],
  },
  guestText: {
    ...typography.body2,
    color: colors.warning.warningDark,
    marginBottom: spacing[3],
  },
  loginButton: {
    alignSelf: 'flex-start',
  },
  searchCard: {
    marginVertical: spacing[2],
    backgroundColor: colors.gray[100],
  },
  searchText: {
    ...typography.body1,
    color: colors.text.tertiary,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: spacing[4],
  },
  actionButton: {
    flex: 1,
    marginHorizontal: spacing[1],
  },
  section: {
    marginVertical: spacing[4],
  },
  sectionTitle: {
    ...typography.h5,
    marginBottom: spacing[3],
  },
  contentCard: {
    marginBottom: spacing[3],
  },
  contentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing[2],
  },
  contentTitle: {
    ...typography.cardTitle,
    flex: 1,
  },
  contentSubject: {
    ...typography.caption,
    color: colors.primary,
    backgroundColor: colors.primaryMuted,
    paddingHorizontal: spacing[2],
    paddingVertical: spacing[1],
    borderRadius: 4,
    marginLeft: spacing[2],
  },
  contentDescription: {
    ...typography.body2,
    marginBottom: spacing[3],
  },
  contentFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  contentDuration: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  contentDifficulty: {
    ...typography.caption,
    color: colors.text.secondary,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing[2],
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: colors.gray[200],
    borderRadius: 2,
    marginRight: spacing[2],
  },
  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  },
  progressText: {
    ...typography.caption,
    color: colors.text.secondary,
    minWidth: 35,
  },
});
