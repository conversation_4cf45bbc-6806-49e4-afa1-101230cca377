import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
  TouchableOpacityProps,
} from 'react-native';
import {colors, layout} from '../../theme';

export interface CardProps extends TouchableOpacityProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: keyof typeof layout | number;
  onPress?: () => void;
}

export const Card: React.FC<CardProps> = ({
  children,
  style,
  variant = 'default',
  padding = 'cardPadding',
  onPress,
  ...props
}) => {
  const cardStyle = [
    styles.base,
    styles[variant],
    typeof padding === 'number'
      ? {padding}
      : {padding: layout[padding as keyof typeof layout]},
    style,
  ];

  if (onPress) {
    return (
      <TouchableOpacity
        style={cardStyle}
        onPress={onPress}
        activeOpacity={0.7}
        {...props}>
        {children}
      </TouchableOpacity>
    );
  }

  return <View style={cardStyle}>{children}</View>;
};

const styles = StyleSheet.create({
  base: {
    borderRadius: layout.cardRadius,
    backgroundColor: colors.card.background,
  },
  default: {
    // No additional styling
  },
  elevated: {
    shadowColor: colors.card.shadow,
    shadowOffset: layout.shadowOffset,
    shadowOpacity: layout.shadowOpacity,
    shadowRadius: layout.shadowRadius,
    elevation: layout.elevation,
  },
  outlined: {
    borderWidth: 1,
    borderColor: colors.card.border,
  },
});
