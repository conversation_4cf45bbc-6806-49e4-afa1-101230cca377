// API Response Types
export interface ApiResponse<T = any> {
  data: T;
  status: number;
  message?: string;
}

export interface ApiError {
  error: string;
  status: number;
}

// User Types
export interface User {
  id: string;
  username: string;
  email: string;
  phone?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

// Auth Types
export interface LoginRequest {
  identifier: string; // username, email or phone
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
}

export interface AuthResponse {
  user: User;
  tokens: AuthTokens;
}

// Content Types
export interface Content {
  id: string;
  title: string;
  description?: string;
  type: 'video' | 'article' | 'pdf' | 'audio';
  subject: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration?: number; // in minutes
  thumbnailUrl?: string;
  contentUrl: string;
  tags: string[];
  isPublished: boolean;
  viewCount: number;
  likeCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface ContentProgress {
  contentId: string;
  userId: string;
  progress: number; // 0-100
  timeSpent: number; // in seconds
  isCompleted: boolean;
  lastAccessedAt: string;
  notes?: string;
}

// Quiz Types
export interface Quiz {
  id: string;
  title: string;
  description?: string;
  subject: string;
  difficulty: 'easy' | 'medium' | 'hard';
  questionCount: number;
  timeLimit?: number; // in minutes
  thumbnailUrl?: string;
  tags: string[];
  isPublished: boolean;
  averageScore: number;
  attemptCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface Question {
  id: string;
  quizId: string;
  question: string;
  options: string[];
  correctAnswer: number; // index of correct option
  explanation?: string;
  difficulty: 'easy' | 'medium' | 'hard';
  points: number;
  order: number;
}

export interface QuizSession {
  id: string;
  quizId: string;
  userId: string;
  startedAt: string;
  endedAt?: string;
  answers: QuizAnswer[];
  score?: number;
  isCompleted: boolean;
}

export interface QuizAnswer {
  questionId: string;
  selectedAnswer: number;
  isCorrect: boolean;
  timeSpent: number; // in seconds
}

export interface QuizResult {
  sessionId: string;
  quiz: Quiz;
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeSpent: number;
  answers: QuizAnswer[];
  completedAt: string;
}

// Topic Types
export interface Topic {
  id: string;
  title: string;
  description?: string;
  parentId?: string;
  order: number;
  level: number; // 0 for root topics
  children?: Topic[];
  contentCount: number;
  quizCount: number;
  isPublished: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TopicProgress {
  topicId: string;
  userId: string;
  progress: number; // 0-100
  completedContentCount: number;
  totalContentCount: number;
  completedQuizCount: number;
  totalQuizCount: number;
  lastAccessedAt: string;
}

// Social Types
export interface FriendRequest {
  id: string;
  senderId: string;
  receiverId: string;
  status: 'pending' | 'accepted' | 'rejected';
  createdAt: string;
  updatedAt: string;
  sender: User;
  receiver: User;
}

export interface Friendship {
  id: string;
  userId: string;
  friendId: string;
  createdAt: string;
  friend: User;
}

export interface TimelineEntry {
  id: string;
  userId: string;
  content: string;
  mediaUrls?: string[];
  privacy: 'public' | 'friends' | 'private';
  likeCount: number;
  commentCount: number;
  isLiked: boolean;
  createdAt: string;
  updatedAt: string;
  user: User;
  comments?: Comment[];
}

export interface Comment {
  id: string;
  timelineEntryId: string;
  userId: string;
  content: string;
  likeCount: number;
  isLiked: boolean;
  createdAt: string;
  updatedAt: string;
  user: User;
}

// Badge Types
export interface Badge {
  id: string;
  name: string;
  description: string;
  iconUrl: string;
  category: string;
  requirements: string;
  points: number;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  isActive: boolean;
  createdAt: string;
}

export interface UserBadge {
  id: string;
  userId: string;
  badgeId: string;
  earnedAt: string;
  badge: Badge;
}

// Progress Types
export interface StudySession {
  id: string;
  userId: string;
  startedAt: string;
  endedAt?: string;
  duration: number; // in seconds
  contentIds: string[];
  quizIds: string[];
  notes?: string;
}

export interface ProgressGoal {
  id: string;
  userId: string;
  type: 'daily' | 'weekly' | 'monthly';
  target: number;
  current: number;
  unit: 'minutes' | 'contents' | 'quizzes' | 'points';
  deadline: string;
  isCompleted: boolean;
  createdAt: string;
}

export interface ProgressStats {
  totalStudyTime: number; // in minutes
  streak: number; // days
  completedContents: number;
  completedQuizzes: number;
  totalPoints: number;
  averageScore: number;
  weeklyProgress: number[];
  monthlyProgress: number[];
}

// Notification Types
export interface Notification {
  id: string;
  userId: string;
  type:
    | 'friend_request'
    | 'quiz_result'
    | 'badge_earned'
    | 'goal_achieved'
    | 'content_liked'
    | 'comment_received';
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: string;
}

// Analytics Types
export interface UserAnalytics {
  userId: string;
  totalStudyTime: number;
  contentViews: number;
  quizAttempts: number;
  averageScore: number;
  streakDays: number;
  badgeCount: number;
  friendCount: number;
  timelinePostCount: number;
  lastActiveAt: string;
}

// Request/Response Types for API calls
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface SearchParams extends PaginationParams {
  query?: string;
  filters?: Record<string, any>;
}

// App State Types
export interface AppState {
  isAuthenticated: boolean;
  user: User | null;
  tokens: AuthTokens | null;
  isLoading: boolean;
  error: string | null;
}

export interface GuestModeData {
  viewedContents: string[];
  attemptedQuizzes: string[];
  lastViewedAt: string;
  limitReached: boolean;
}
