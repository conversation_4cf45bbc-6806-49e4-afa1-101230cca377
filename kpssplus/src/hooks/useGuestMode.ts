import {useState, useEffect, useCallback} from 'react';
import {GuestModeData} from '../types';
import {apiClient} from '../services/api';

interface GuestModeState {
  data: GuestModeData | null;
  isLimitReached: boolean;
  contentViewCount: number;
  quizAttemptCount: number;
  isLoading: boolean;
}

interface GuestModeActions {
  trackContentView: (contentId: string) => Promise<void>;
  trackQuizAttempt: (quizId: string) => Promise<void>;
  clearGuestData: () => Promise<void>;
  checkContentLimit: () => boolean;
  checkQuizLimit: () => boolean;
  getRemainingContentViews: () => number;
  getRemainingQuizAttempts: () => number;
}

const GUEST_LIMITS = {
  CONTENT_VIEWS: 10,
  QUIZ_ATTEMPTS: 3,
} as const;

export const useGuestMode = (): GuestModeState & GuestModeActions => {
  const [state, setState] = useState<GuestModeState>({
    data: null,
    isLimitReached: false,
    contentViewCount: 0,
    quizAttemptCount: 0,
    isLoading: true,
  });

  // Load guest data on mount
  useEffect(() => {
    loadGuestData();
  }, []);

  const loadGuestData = async () => {
    try {
      setState(prev => ({...prev, isLoading: true}));

      const guestData = await apiClient.getGuestData();

      if (guestData) {
        const contentViewCount = guestData.viewedContents?.length || 0;
        const quizAttemptCount = guestData.attemptedQuizzes?.length || 0;
        const isLimitReached =
          contentViewCount >= GUEST_LIMITS.CONTENT_VIEWS ||
          quizAttemptCount >= GUEST_LIMITS.QUIZ_ATTEMPTS;

        setState(prev => ({
          ...prev,
          data: guestData,
          contentViewCount,
          quizAttemptCount,
          isLimitReached,
          isLoading: false,
        }));
      } else {
        // Initialize empty guest data
        const initialData: GuestModeData = {
          viewedContents: [],
          attemptedQuizzes: [],
          lastViewedAt: new Date().toISOString(),
          limitReached: false,
        };

        await apiClient.setGuestData(initialData);

        setState(prev => ({
          ...prev,
          data: initialData,
          contentViewCount: 0,
          quizAttemptCount: 0,
          isLimitReached: false,
          isLoading: false,
        }));
      }
    } catch (error) {
      console.error('Error loading guest data:', error);
      setState(prev => ({...prev, isLoading: false}));
    }
  };

  const trackContentView = useCallback(
    async (contentId: string) => {
      try {
        const currentData = state.data || {
          viewedContents: [],
          attemptedQuizzes: [],
          lastViewedAt: new Date().toISOString(),
          limitReached: false,
        };

        // Don't track if already viewed
        if (currentData.viewedContents.includes(contentId)) {
          return;
        }

        const updatedData: GuestModeData = {
          ...currentData,
          viewedContents: [...currentData.viewedContents, contentId],
          lastViewedAt: new Date().toISOString(),
          limitReached:
            currentData.viewedContents.length + 1 >= GUEST_LIMITS.CONTENT_VIEWS,
        };

        await apiClient.setGuestData(updatedData);

        setState(prev => ({
          ...prev,
          data: updatedData,
          contentViewCount: updatedData.viewedContents.length,
          isLimitReached:
            updatedData.limitReached ||
            prev.quizAttemptCount >= GUEST_LIMITS.QUIZ_ATTEMPTS,
        }));
      } catch (error) {
        console.error('Error tracking content view:', error);
      }
    },
    [state.data],
  );

  const trackQuizAttempt = useCallback(
    async (quizId: string) => {
      try {
        const currentData = state.data || {
          viewedContents: [],
          attemptedQuizzes: [],
          lastViewedAt: new Date().toISOString(),
          limitReached: false,
        };

        // Don't track if already attempted
        if (currentData.attemptedQuizzes.includes(quizId)) {
          return;
        }

        const updatedData: GuestModeData = {
          ...currentData,
          attemptedQuizzes: [...currentData.attemptedQuizzes, quizId],
          lastViewedAt: new Date().toISOString(),
          limitReached:
            currentData.attemptedQuizzes.length + 1 >=
            GUEST_LIMITS.QUIZ_ATTEMPTS,
        };

        await apiClient.setGuestData(updatedData);

        setState(prev => ({
          ...prev,
          data: updatedData,
          quizAttemptCount: updatedData.attemptedQuizzes.length,
          isLimitReached:
            updatedData.limitReached ||
            prev.contentViewCount >= GUEST_LIMITS.CONTENT_VIEWS,
        }));
      } catch (error) {
        console.error('Error tracking quiz attempt:', error);
      }
    },
    [state.data],
  );

  const clearGuestData = useCallback(async () => {
    try {
      await apiClient.clearGuestData();

      const initialData: GuestModeData = {
        viewedContents: [],
        attemptedQuizzes: [],
        lastViewedAt: new Date().toISOString(),
        limitReached: false,
      };

      setState(prev => ({
        ...prev,
        data: initialData,
        contentViewCount: 0,
        quizAttemptCount: 0,
        isLimitReached: false,
      }));
    } catch (error) {
      console.error('Error clearing guest data:', error);
    }
  }, []);

  const checkContentLimit = useCallback(() => {
    return state.contentViewCount >= GUEST_LIMITS.CONTENT_VIEWS;
  }, [state.contentViewCount]);

  const checkQuizLimit = useCallback(() => {
    return state.quizAttemptCount >= GUEST_LIMITS.QUIZ_ATTEMPTS;
  }, [state.quizAttemptCount]);

  const getRemainingContentViews = useCallback(() => {
    return Math.max(0, GUEST_LIMITS.CONTENT_VIEWS - state.contentViewCount);
  }, [state.contentViewCount]);

  const getRemainingQuizAttempts = useCallback(() => {
    return Math.max(0, GUEST_LIMITS.QUIZ_ATTEMPTS - state.quizAttemptCount);
  }, [state.quizAttemptCount]);

  return {
    ...state,
    trackContentView,
    trackQuizAttempt,
    clearGuestData,
    checkContentLimit,
    checkQuizLimit,
    getRemainingContentViews,
    getRemainingQuizAttempts,
  };
};
