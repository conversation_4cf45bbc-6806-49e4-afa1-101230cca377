import {apiClient} from './api';
import {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  AuthTokens,
  User,
  ApiResponse,
} from '../types';

export class AuthService {
  // Login with username/email/phone
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>(
      '/auth/login',
      credentials,
    );

    // Store tokens and user data
    await apiClient.setAuthTokens(response.data.tokens);

    return response.data;
  }

  // Login with specific identifier type
  async loginWithUsername(
    username: string,
    password: string,
  ): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>(
      '/auth/login/username',
      {
        username,
        password,
      },
    );

    await apiClient.setAuthTokens(response.data.tokens);
    return response.data;
  }

  async loginWithEmail(email: string, password: string): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/login/email', {
      email,
      password,
    });

    await apiClient.setAuthTokens(response.data.tokens);
    return response.data;
  }

  async loginWithPhone(phone: string, password: string): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/login/phone', {
      phone,
      password,
    });

    await apiClient.setAuthTokens(response.data.tokens);
    return response.data;
  }

  // Social login
  async loginWithGoogle(googleToken: string): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/login/google', {
      token: googleToken,
    });

    await apiClient.setAuthTokens(response.data.tokens);
    return response.data;
  }

  async loginWithApple(appleToken: string): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/login/apple', {
      token: appleToken,
    });

    await apiClient.setAuthTokens(response.data.tokens);
    return response.data;
  }

  // Registration
  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>(
      '/auth/register',
      userData,
    );

    await apiClient.setAuthTokens(response.data.tokens);
    return response.data;
  }

  // Password reset
  async forgotPassword(email: string): Promise<ApiResponse> {
    return await apiClient.post('/auth/forgot-password', {email});
  }

  async resetPassword(
    token: string,
    newPassword: string,
  ): Promise<ApiResponse> {
    return await apiClient.post('/auth/reset-password', {
      token,
      password: newPassword,
    });
  }

  async verifyOTP(phone: string, otp: string): Promise<ApiResponse> {
    return await apiClient.post('/auth/verify-otp', {phone, otp});
  }

  // Token validation
  async validateToken(): Promise<ApiResponse<{valid: boolean; user?: User}>> {
    return await apiClient.post('/auth/validate-token');
  }

  // Logout
  async logout(): Promise<void> {
    try {
      // Optionally call logout endpoint if available
      // await apiClient.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear local auth data
      await apiClient.clearAuthTokens();
    }
  }

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    const tokens = await apiClient.getAuthTokens();
    if (!tokens) {
      return false;
    }

    try {
      const response = await this.validateToken();
      return response.data.valid;
    } catch (error) {
      await apiClient.clearAuthTokens();
      return false;
    }
  }

  // Get current user profile
  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get<User>('/user/profile');
    return response.data;
  }

  // Update user profile
  async updateProfile(userData: Partial<User>): Promise<User> {
    const response = await apiClient.put<User>('/user/profile', userData);
    return response.data;
  }

  // Change password
  async changePassword(
    currentPassword: string,
    newPassword: string,
  ): Promise<ApiResponse> {
    return await apiClient.put('/user/password', {
      currentPassword,
      newPassword,
    });
  }

  // Email verification
  async verifyEmail(token: string): Promise<ApiResponse> {
    return await apiClient.post('/user/verify-email', {token});
  }

  async resendVerificationEmail(): Promise<ApiResponse> {
    return await apiClient.post('/user/resend-verification');
  }

  // Account management
  async deactivateAccount(): Promise<ApiResponse> {
    return await apiClient.post('/user/deactivate');
  }

  async deleteAccount(): Promise<ApiResponse> {
    const response = await apiClient.delete('/user/delete');
    await apiClient.clearAuthTokens();
    return response;
  }

  // Notification settings
  async updateNotificationSettings(settings: any): Promise<ApiResponse> {
    return await apiClient.put('/user/notifications', settings);
  }

  // Get stored auth tokens
  async getAuthTokens(): Promise<AuthTokens | null> {
    return await apiClient.getAuthTokens();
  }
}

// Export singleton instance
export const authService = new AuthService();
