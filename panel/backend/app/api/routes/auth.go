package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/kpss-plus-backend/pkg/config"
	"github.com/kpss-plus-backend/pkg/domains/auth"
	"github.com/kpss-plus-backend/pkg/dtos"
	"github.com/kpss-plus-backend/pkg/entities"
	"github.com/kpss-plus-backend/pkg/kpsslog"
	"github.com/kpss-plus-backend/pkg/utils"
)

func AuthRoutes(r *gin.RouterGroup, s auth.Service) {
	// Legacy endpoints
	r.POST("/login", Login(s))

	// Enhanced authentication endpoints
	r.POST("/register", Register(s))
	r.POST("/login/username", LoginWithUsername(s))
	r.POST("/login/email", LoginWithEmail(s))
	r.POST("/login/phone", LoginWithPhone(s))

	// Admin authentication
	r.POST("/login/admin", AdminLogin(s))
	r.GET("/admin/check", AdminCheck(s))

	// Social login endpoints
	r.POST("/login/google", LoginWithGoogle(s))
	r.POST("/login/apple", LoginWithApple(s))

	// Password reset endpoints
	r.POST("/forgot-password", ForgotPassword(s))
	r.POST("/reset-password", ResetPassword(s))
	r.POST("/verify-otp", VerifyOTP(s))

	// Token validation endpoint
	r.POST("/validate-token", ValidateToken(s))
}

func Login(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.AuthenticationRequest
		if err := c.ShouldBindJSON(&req); err != nil {

			kpsslog.CreateLog(&entities.Log{
				Title:   "Login Failed",
				Message: "Login Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.Login(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}
		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Enhanced authentication handlers
func Register(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.RegisterRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			kpsslog.CreateLog(&entities.Log{
				Title:   "Register Failed",
				Message: "Register Failed, bind json err:" + err.Error(),
				Entity:  "user",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.Register(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(201, gin.H{
			"data":   resp,
			"status": 201,
		})
	}
}

func LoginWithUsername(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.LoginRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.LoginWithUsername(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func LoginWithEmail(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.EmailLoginRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.LoginWithEmail(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func LoginWithPhone(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.PhoneLoginRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.LoginWithPhone(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Social login handlers
func LoginWithGoogle(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.GoogleLoginRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.LoginWithGoogle(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func LoginWithApple(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.AppleLoginRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		resp, err := s.LoginWithApple(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

// Password reset handlers
func ForgotPassword(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.ForgotPasswordRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		err := s.ForgotPassword(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "Password reset email sent",
			"status":  200,
		})
	}
}

func ResetPassword(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.ResetPasswordRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		err := s.ResetPassword(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "Password reset successfully",
			"status":  200,
		})
	}
}

func VerifyOTP(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.VerifyOTPRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		err := s.VerifyOTP(&req)
		if err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		c.JSON(200, gin.H{
			"message": "OTP verified successfully",
			"status":  200,
		})
	}
}

func AdminLogin(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req dtos.AuthenticationRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			kpsslog.CreateLog(&entities.Log{
				Title:   "Admin Login Failed",
				Message: "Bind json err:" + err.Error(),
				Entity:  "admin",
				Type:    "error",
				Ip:      c.ClientIP(),
			})

			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		kpsslog.CreateLog(&entities.Log{
			Title:   "Admin Login Attempt",
			Message: "Username: " + req.Username,
			Entity:  "admin",
			Type:    "info",
			Ip:      c.ClientIP(),
		})

		resp, err := s.AdminLogin(&req)
		if err != nil {
			kpsslog.CreateLog(&entities.Log{
				Title:   "Admin Login Failed",
				Message: "Error: " + err.Error(),
				Entity:  "admin",
				Type:    "error",
				Ip:      c.ClientIP(),
			})
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		kpsslog.CreateLog(&entities.Log{
			Title:   "Admin Login Success",
			Message: "Username: " + req.Username + " logged in successfully",
			Entity:  "admin",
			Type:    "success",
			Ip:      c.ClientIP(),
		})

		c.JSON(200, gin.H{
			"data":   resp,
			"status": 200,
		})
	}
}

func AdminCheck(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		username := c.Query("username")
		if username == "" {
			c.JSON(400, gin.H{"error": "username parameter required"})
			return
		}

		user, err := s.GetUserByUserName(username)
		if err != nil {
			c.JSON(404, gin.H{
				"error":    "User not found",
				"username": username,
			})
			return
		}

		c.JSON(200, gin.H{
			"username":   username,
			"is_admin":   user.IsAdmin,
			"email":      user.Email,
			"created_at": user.CreatedAt,
		})
	}
}

func ValidateToken(s auth.Service) func(c *gin.Context) {
	return func(c *gin.Context) {
		var req struct {
			Token string `json:"token" validate:"required"`
		}
		if err := c.ShouldBindJSON(&req); err != nil {
			c.AbortWithStatusJSON(400, gin.H{
				"error":  err.Error(),
				"status": 400,
			})
			return
		}

		userID, err := s.ValidateToken(req.Token)
		if err != nil {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "Invalid token",
				"status": 401,
			})
			return
		}

		// Parse token to check if it's admin or user
		jwt := utils.JwtWrapper{
			SecretKey: config.ReadValue().App.JwtSecret,
		}

		claims, err := jwt.ParseToken(req.Token)
		if err != nil {
			c.AbortWithStatusJSON(401, gin.H{
				"error":  "Invalid token",
				"status": 401,
			})
			return
		}

		response := gin.H{
			"valid":   true,
			"user_id": userID,
		}

		if claims.AdminID != "" {
			response["user_type"] = "admin"
			response["admin_id"] = claims.AdminID
		} else {
			response["user_type"] = "user"
		}

		c.JSON(200, gin.H{
			"data":   response,
			"status": 200,
		})
	}
}
