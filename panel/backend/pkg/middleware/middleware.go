package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/kpss-plus-backend/pkg/config"
	"github.com/kpss-plus-backend/pkg/localizer"
	"github.com/kpss-plus-backend/pkg/state"
	"github.com/kpss-plus-backend/pkg/utils"
)

func ClaimIp() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("CurrentIP", c.ClientIP())
		c.Set(state.CurrentUserIP, c.ClientIP())
		c.Next()
	}
}

func FromClient() gin.HandlerFunc {
	return func(c *gin.Context) {
		client_id := c.GetHeader("client_id")
		if client_id == config.InitConfig().App.ClientID {
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("error_unauthorized_client", c.Get<PERSON>(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}

func Authorized() gin.HandlerFunc {
    return func(c *gin.Context) {
		cfg_app := config.InitConfig().App
		jwt := utils.JwtWrapper{
			Issuer:    cfg_app.JwtIssuer,
			SecretKey: cfg_app.JwtSecret,
		}
		bearer := c.Request.Header.Get("Authorization")
		if bearer == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_required_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		if !strings.HasPrefix(bearer, "Bearer ") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_incorrect_format_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		token := strings.Split(bearer, "Bearer ")[1]
		if jwt.ValidateToken(token) {
			claims, _ := jwt.ParseToken(token)
			c.Set(state.CurrentUserID, claims.UserID)
			c.Set(state.CurrentAdminID, claims.AdminID)
			c.Set(state.CurrentDeviceID, claims.DeviceID)
			c.Set(state.CurrentUserIP, c.ClientIP())
			c.Set(state.CurrentTimezone, claims.Timezone)
			c.Set(state.CurrentPhoneLanguage, claims.PhoneLanguage)
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_invalid_or_expired_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
    }
}

// OptionalAuthorized allows requests to proceed without a token.
// If a valid Bearer token is provided, user/admin/device claims are
// populated into the context just like Authorized(). If no token is
// provided, the request continues as a guest.
func OptionalAuthorized() gin.HandlerFunc {
    return func(c *gin.Context) {
        cfg_app := config.InitConfig().App
        jwt := utils.JwtWrapper{
            Issuer:    cfg_app.JwtIssuer,
            SecretKey: cfg_app.JwtSecret,
        }

        bearer := c.Request.Header.Get("Authorization")
        if bearer == "" {
            // No auth header: continue as guest
            c.Next()
            return
        }
        if !strings.HasPrefix(bearer, "Bearer ") {
            c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_incorrect_format_error", c.GetString(state.CurrentPhoneLanguage), nil)})
            return
        }
        token := strings.Split(bearer, "Bearer ")[1]
        if jwt.ValidateToken(token) {
            claims, _ := jwt.ParseToken(token)
            c.Set(state.CurrentUserID, claims.UserID)
            c.Set(state.CurrentAdminID, claims.AdminID)
            c.Set(state.CurrentDeviceID, claims.DeviceID)
            c.Set(state.CurrentUserIP, c.ClientIP())
            c.Set(state.CurrentTimezone, claims.Timezone)
            c.Set(state.CurrentPhoneLanguage, claims.PhoneLanguage)
            c.Next()
        } else {
            // Invalid token provided: continue as guest (do not block public access)
            c.Next()
        }
    }
}

func AdminAuthorized() gin.HandlerFunc {
	return func(c *gin.Context) {
		cfg_app := config.InitConfig().App
		jwt := utils.JwtWrapper{
			Issuer:    cfg_app.JwtIssuer,
			SecretKey: cfg_app.JwtSecret,
		}
		bearer := c.Request.Header.Get("Authorization")
		if bearer == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_required_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		if !strings.HasPrefix(bearer, "Bearer ") {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_incorrect_format_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
		token := strings.Split(bearer, "Bearer ")[1]
		if jwt.ValidateToken(token) {
			claims, _ := jwt.ParseToken(token)
			if claims.AdminID == "" {
				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Admin access required"})
				return
			}
			c.Set(state.CurrentUserID, claims.UserID)
			c.Set(state.CurrentAdminID, claims.AdminID)
			c.Set(state.CurrentDeviceID, claims.DeviceID)
			c.Set(state.CurrentUserIP, c.ClientIP())
			c.Set(state.CurrentTimezone, claims.Timezone)
			c.Set(state.CurrentPhoneLanguage, claims.PhoneLanguage)
			c.Next()
		} else {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": localizer.GetTranslated("authorization_token_invalid_or_expired_error", c.GetString(state.CurrentPhoneLanguage), nil)})
			return
		}
	}
}
