import React, { useState, useEffect } from 'react';
import {
  BookOpen,
  Folder,
  Search,
  Grid,
  List,
  Star,
  Clock,
  Plus,
  MoreVertical,
  FileText,
  Video
} from 'lucide-react';
import { libraryService } from '../../services/libraryService';
import { useToast } from '../common/Toast';

interface LibraryItem {
  id: string;
  type: 'quiz' | 'content' | 'video' | 'document';
  title: string;
  description?: string;
  thumbnail?: string;
  url?: string;
  content_id?: string;
  quiz_id?: string;
  folder_id?: string;
  tags: string[];
  is_favorite: boolean;
  added_at: string;
  last_accessed?: string;
  progress?: number;
  metadata?: any;
}

interface LibraryFolder {
  id: string;
  name: string;
  description?: string;
  color?: string;
  icon?: string;
  parent_id?: string;
  items_count: number;
  created_at: string;
  updated_at: string;
}

const LibraryPage: React.FC = () => {
  const { showError, showSuccess } = useToast();
  const [items, setItems] = useState<LibraryItem[]>([]);
  const [folders, setFolders] = useState<LibraryFolder[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeTab, setActiveTab] = useState<'all' | 'favorites' | 'recent'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null);
  const [filterType, setFilterType] = useState<string>('all');

  useEffect(() => {
    loadLibraryData();
  }, [activeTab, selectedFolder, filterType]);

  const loadLibraryData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use actual API calls instead of mock data
      let itemsResponse;

      // Call different endpoints based on active tab
      switch (activeTab) {
        case 'favorites':
          itemsResponse = await libraryService.getFavoriteItems({
            page: 1,
            limit: 50
          });
          break;
        case 'recent':
          itemsResponse = await libraryService.getRecentItems({
            page: 1,
            limit: 50
          });
          break;
        default:
          itemsResponse = await libraryService.getLibraryItems({
            type: filterType === 'all' ? undefined : filterType as any,
            folder_id: selectedFolder || undefined,
            page: 1,
            limit: 50
          });
      }

      const foldersResponse = await libraryService.getFolders();

      if (itemsResponse.success && itemsResponse.data) {
        setItems(itemsResponse.data);
      }

      if (foldersResponse.success && foldersResponse.data) {
        setFolders(foldersResponse.data.folders?.map(folder => ({
          ...folder,
          items_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })) || []);
      }

    } catch (err) {
      const errorMsg = 'Backend service unavailable';
      setError(errorMsg);
      showError('Service Error', errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchQuery.trim()) {
      loadLibraryData(); // Reload all data if search is empty
      return;
    }

    try {
      // Use actual search API
      const response = await libraryService.searchLibraryItems(searchQuery, {
        type: (activeTab === 'all' || activeTab === 'favorites' || activeTab === 'recent') ? undefined : activeTab as any,
        limit: 50
      });

      if (response.success && response.data) {
        setItems(response.data);

        if (response.data.length === 0) {
          showError('No results found', 'Try different search terms');
        }
      }
    } catch (err) {
      showError('Search failed', 'Unable to search library items');
    }
  };

  const handleToggleFavorite = async (itemId: string) => {
    try {
      setItems(prev =>
        prev.map(item =>
          item.id === itemId
            ? { ...item, is_favorite: !item.is_favorite }
            : item
        )
      );
      showSuccess('Updated', 'Favorite status updated');
    } catch (err) {
      showError('Failed to update favorite', 'Please try again');
    }
  };



  const getItemIcon = (type: string) => {
    switch (type) {
      case 'quiz': return BookOpen;
      case 'video': return Video;
      case 'document': return FileText;
      default: return FileText;
    }
  };

  const getItemColor = (type: string) => {
    switch (type) {
      case 'quiz': return 'text-blue-400';
      case 'video': return 'text-red-400';
      case 'document': return 'text-green-400';
      default: return 'text-gray-400';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 mb-2">Error loading library</div>
        <p className="text-dark-400">{error}</p>
        <button
          onClick={loadLibraryData}
          className="mt-4 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white">Library</h1>
          <p className="text-dark-300 mt-1">Your saved content and study materials</p>
        </div>

        <div className="flex items-center gap-3">
          {/* View Mode Toggle */}
          <div className="flex bg-dark-800 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'grid' 
                  ? 'bg-primary-600 text-white' 
                  : 'text-dark-400 hover:text-white'
              }`}
            >
              <Grid size={16} />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors ${
                viewMode === 'list' 
                  ? 'bg-primary-600 text-white' 
                  : 'text-dark-400 hover:text-white'
              }`}
            >
              <List size={16} />
            </button>
          </div>

          <button className="flex items-center gap-2 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors">
            <Plus size={16} />
            Add to Library
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col lg:flex-row gap-4">
        {/* Search */}
        <form onSubmit={handleSearch} className="flex gap-2 flex-1">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-dark-400" size={20} />
            <input
              type="text"
              placeholder="Search your library..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white placeholder-dark-400 focus:outline-none focus:border-primary-500"
            />
          </div>
          <button
            type="submit"
            className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
          >
            <Search size={20} />
          </button>
        </form>

        {/* Filters */}
        <div className="flex gap-2">
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
          >
            <option value="all">All Types</option>
            <option value="quiz">Quizzes</option>
            <option value="video">Videos</option>
            <option value="document">Documents</option>
            <option value="content">Content</option>
          </select>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-dark-800 p-1 rounded-lg">
        {[
          { key: 'all', label: 'All Items', icon: BookOpen },
          { key: 'favorites', label: 'Favorites', icon: Star },
          { key: 'recent', label: 'Recent', icon: Clock }
        ].map(({ key, label, icon: Icon }) => (
          <button
            key={key}
            onClick={() => setActiveTab(key as typeof activeTab)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === key
                ? 'bg-primary-600 text-white'
                : 'text-dark-300 hover:text-white hover:bg-dark-700'
            }`}
          >
            <Icon size={16} />
            {label}
          </button>
        ))}
      </div>

      {/* Breadcrumb */}
      {selectedFolder && (
        <div className="flex items-center gap-2 text-sm">
          <button
            onClick={() => setSelectedFolder(null)}
            className="text-primary-400 hover:text-primary-300 transition-colors"
          >
            Library
          </button>
          <span className="text-dark-400">/</span>
          <span className="text-white">
            {folders.find(f => f.id === selectedFolder)?.name || 'Unknown Folder'}
          </span>
        </div>
      )}

      {/* Content */}
      <div className="bg-dark-800 rounded-xl border border-dark-700">
        {/* Folders */}
        {!selectedFolder && folders.length > 0 && (
          <div className="p-6 border-b border-dark-700">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
              <Folder className="text-yellow-400" size={20} />
              Folders
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {folders.map((folder) => (
                <button
                  key={folder.id}
                  onClick={() => setSelectedFolder(folder.id)}
                  className="flex items-center gap-3 p-4 bg-dark-700 hover:bg-dark-600 rounded-lg transition-colors text-left"
                >
                  <Folder className="text-yellow-400" size={24} />
                  <div className="flex-1 min-w-0">
                    <h4 className="text-white font-medium truncate">{folder.name}</h4>
                    <p className="text-dark-400 text-sm">{folder.items_count} items</p>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}

        {/* Items */}
        <div className="p-6">
          {items.length === 0 ? (
            <div className="text-center py-12">
              <BookOpen className="mx-auto h-12 w-12 text-dark-400 mb-4" />
              <h4 className="text-lg font-medium text-white mb-2">No items found</h4>
              <p className="text-dark-400">
                {activeTab === 'favorites' 
                  ? 'You haven\'t favorited any items yet'
                  : activeTab === 'recent'
                  ? 'No recent items to show'
                  : 'Your library is empty. Start adding content to see it here.'
                }
              </p>
            </div>
          ) : viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {items.map((item) => {
                const Icon = getItemIcon(item.type);
                const iconColor = getItemColor(item.type);
                
                return (
                  <div key={item.id} className="bg-dark-700 rounded-lg p-4 hover:bg-dark-600 transition-colors">
                    <div className="flex items-start justify-between mb-3">
                      <div className={`${iconColor}`}>
                        <Icon size={24} />
                      </div>
                      <div className="flex items-center gap-1">
                        <button
                          onClick={() => handleToggleFavorite(item.id)}
                          className={`p-1 rounded transition-colors ${
                            item.is_favorite 
                              ? 'text-yellow-400 hover:text-yellow-300' 
                              : 'text-dark-400 hover:text-yellow-400'
                          }`}
                        >
                          <Star size={16} fill={item.is_favorite ? 'currentColor' : 'none'} />
                        </button>
                        <button className="p-1 text-dark-400 hover:text-white transition-colors">
                          <MoreVertical size={16} />
                        </button>
                      </div>
                    </div>
                    
                    <h4 className="text-white font-medium mb-2 line-clamp-2">{item.title}</h4>
                    {item.description && (
                      <p className="text-dark-400 text-sm mb-3 line-clamp-2">{item.description}</p>
                    )}
                    
                    {item.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mb-3">
                        {item.tags.slice(0, 3).map((tag, index) => (
                          <span key={index} className="px-2 py-1 bg-dark-600 text-dark-300 text-xs rounded">
                            {tag}
                          </span>
                        ))}
                        {item.tags.length > 3 && (
                          <span className="px-2 py-1 bg-dark-600 text-dark-300 text-xs rounded">
                            +{item.tags.length - 3}
                          </span>
                        )}
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between text-xs text-dark-400">
                      <span>Added {formatDate(item.added_at)}</span>
                      {item.progress !== undefined && (
                        <span>{item.progress}% complete</span>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="space-y-2">
              {items.map((item) => {
                const Icon = getItemIcon(item.type);
                const iconColor = getItemColor(item.type);
                
                return (
                  <div key={item.id} className="flex items-center gap-4 p-4 bg-dark-700 hover:bg-dark-600 rounded-lg transition-colors">
                    <div className={`${iconColor}`}>
                      <Icon size={20} />
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h4 className="text-white font-medium truncate">{item.title}</h4>
                      <p className="text-dark-400 text-sm truncate">{item.description}</p>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-dark-400">
                      <span>{formatDate(item.added_at)}</span>
                      {item.progress !== undefined && (
                        <span>{item.progress}%</span>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-1">
                      <button
                        onClick={() => handleToggleFavorite(item.id)}
                        className={`p-2 rounded transition-colors ${
                          item.is_favorite 
                            ? 'text-yellow-400 hover:text-yellow-300' 
                            : 'text-dark-400 hover:text-yellow-400'
                        }`}
                      >
                        <Star size={16} fill={item.is_favorite ? 'currentColor' : 'none'} />
                      </button>
                      <button className="p-2 text-dark-400 hover:text-white transition-colors">
                        <MoreVertical size={16} />
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LibraryPage;
