import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  BarChart3, 
  TrendingUp, 
  Target,
  Clock,
  Calendar,
  Award,
  Star,
  Trophy,
  BookOpen,
  Users,
  Activity,
  Zap,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface AnalyticsData {
  overview: {
    total_study_time: number;
    quizzes_completed: number;
    average_score: number;
    current_streak: number;
    total_points: number;
    level: number;
  };
  performance: {
    weekly_scores: number[];
    subject_performance: Array<{
      subject: string;
      score: number;
      improvement: number;
    }>;
    difficulty_breakdown: Array<{
      difficulty: string;
      correct: number;
      total: number;
    }>;
  };
  activity: {
    daily_activity: Array<{
      date: string;
      study_time: number;
      quizzes: number;
    }>;
    peak_hours: Array<{
      hour: number;
      activity_count: number;
    }>;
  };
  achievements: Array<{
    id: string;
    name: string;
    description: string;
    icon: string;
    earned_at: string;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
  }>;
}

const AnalyticsPage: React.FC = () => {
  const { t } = useTranslation();
  const [data, setData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('month');

  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Mock data - replace with actual API call
      const mockData: AnalyticsData = {
        overview: {
          total_study_time: 12450, // seconds
          quizzes_completed: 47,
          average_score: 78.5,
          current_streak: 12,
          total_points: 2340,
          level: 8
        },
        performance: {
          weekly_scores: [65, 72, 78, 81, 75, 83, 79],
          subject_performance: [
            { subject: 'Mathematics', score: 85, improvement: 5 },
            { subject: 'Turkish', score: 78, improvement: -2 },
            { subject: 'History', score: 82, improvement: 8 },
            { subject: 'Geography', score: 74, improvement: 3 },
            { subject: 'Science', score: 88, improvement: 12 }
          ],
          difficulty_breakdown: [
            { difficulty: 'Easy', correct: 45, total: 50 },
            { difficulty: 'Medium', correct: 32, total: 45 },
            { difficulty: 'Hard', correct: 18, total: 30 }
          ]
        },
        activity: {
          daily_activity: [
            { date: '2024-01-01', study_time: 3600, quizzes: 3 },
            { date: '2024-01-02', study_time: 2400, quizzes: 2 },
            { date: '2024-01-03', study_time: 4200, quizzes: 4 },
            { date: '2024-01-04', study_time: 1800, quizzes: 1 },
            { date: '2024-01-05', study_time: 3000, quizzes: 3 },
            { date: '2024-01-06', study_time: 3600, quizzes: 2 },
            { date: '2024-01-07', study_time: 2700, quizzes: 2 }
          ],
          peak_hours: [
            { hour: 9, activity_count: 15 },
            { hour: 14, activity_count: 22 },
            { hour: 20, activity_count: 18 },
            { hour: 21, activity_count: 25 }
          ]
        },
        achievements: [
          {
            id: '1',
            name: 'First Steps',
            description: 'Complete your first quiz',
            icon: '🎯',
            earned_at: '2024-01-01',
            rarity: 'common'
          },
          {
            id: '2',
            name: 'Speed Demon',
            description: 'Complete a quiz in under 5 minutes',
            icon: '⚡',
            earned_at: '2024-01-03',
            rarity: 'rare'
          },
          {
            id: '3',
            name: 'Perfect Score',
            description: 'Get 100% on a quiz',
            icon: '🏆',
            earned_at: '2024-01-05',
            rarity: 'epic'
          }
        ]
      };

      setData(mockData);
    } catch (err) {
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'text-gray-400 border-gray-400';
      case 'rare': return 'text-blue-400 border-blue-400';
      case 'epic': return 'text-purple-400 border-purple-400';
      case 'legendary': return 'text-yellow-400 border-yellow-400';
      default: return 'text-gray-400 border-gray-400';
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 mb-2">Error loading analytics</div>
        <p className="text-dark-400">{error}</p>
        <button
          onClick={loadAnalyticsData}
          className="mt-4 px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white">Analytics</h1>
          <p className="text-dark-300 mt-1">Track your learning progress and performance</p>
        </div>

        <div className="flex items-center gap-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value as typeof timeRange)}
            className="bg-dark-700 border border-dark-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-primary-500"
          >
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="year">This Year</option>
          </select>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
          <div className="flex items-center gap-3 mb-3">
            <Clock className="text-blue-400" size={24} />
            <h3 className="text-white font-semibold">Study Time</h3>
          </div>
          <div className="text-2xl font-bold text-white">{formatTime(data.overview.total_study_time)}</div>
        </div>

        <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
          <div className="flex items-center gap-3 mb-3">
            <BookOpen className="text-green-400" size={24} />
            <h3 className="text-white font-semibold">Quizzes</h3>
          </div>
          <div className="text-2xl font-bold text-white">{data.overview.quizzes_completed}</div>
        </div>

        <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
          <div className="flex items-center gap-3 mb-3">
            <Target className="text-yellow-400" size={24} />
            <h3 className="text-white font-semibold">Avg Score</h3>
          </div>
          <div className="text-2xl font-bold text-white">{data.overview.average_score}%</div>
        </div>

        <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
          <div className="flex items-center gap-3 mb-3">
            <Zap className="text-orange-400" size={24} />
            <h3 className="text-white font-semibold">Streak</h3>
          </div>
          <div className="text-2xl font-bold text-white">{data.overview.current_streak} days</div>
        </div>

        <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
          <div className="flex items-center gap-3 mb-3">
            <Star className="text-purple-400" size={24} />
            <h3 className="text-white font-semibold">Points</h3>
          </div>
          <div className="text-2xl font-bold text-white">{data.overview.total_points}</div>
        </div>

        <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
          <div className="flex items-center gap-3 mb-3">
            <Trophy className="text-primary-400" size={24} />
            <h3 className="text-white font-semibold">Level</h3>
          </div>
          <div className="text-2xl font-bold text-white">{data.overview.level}</div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Subject Performance */}
        <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
          <h3 className="text-lg font-semibold text-white mb-6 flex items-center gap-2">
            <BarChart3 className="text-primary-400" size={20} />
            Subject Performance
          </h3>
          
          <div className="space-y-4">
            {data.performance.subject_performance.map((subject, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-white font-medium">{subject.subject}</span>
                  <div className="flex items-center gap-2">
                    <span className="text-white">{subject.score}%</span>
                    <span className={`text-sm ${
                      subject.improvement > 0 ? 'text-green-400' : 
                      subject.improvement < 0 ? 'text-red-400' : 'text-gray-400'
                    }`}>
                      {subject.improvement > 0 ? '+' : ''}{subject.improvement}%
                    </span>
                  </div>
                </div>
                <div className="w-full bg-dark-700 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-primary-500 to-secondary-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${subject.score}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Difficulty Breakdown */}
        <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
          <h3 className="text-lg font-semibold text-white mb-6 flex items-center gap-2">
            <TrendingUp className="text-green-400" size={20} />
            Difficulty Breakdown
          </h3>
          
          <div className="space-y-4">
            {data.performance.difficulty_breakdown.map((item, index) => {
              const percentage = (item.correct / item.total) * 100;
              return (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-white font-medium">{item.difficulty}</span>
                    <span className="text-white">{item.correct}/{item.total} ({percentage.toFixed(0)}%)</span>
                  </div>
                  <div className="w-full bg-dark-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${
                        item.difficulty === 'Easy' ? 'bg-green-500' :
                        item.difficulty === 'Medium' ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Achievements */}
      <div className="bg-dark-800 rounded-xl p-6 border border-dark-700">
        <h3 className="text-lg font-semibold text-white mb-6 flex items-center gap-2">
          <Award className="text-yellow-400" size={20} />
          Recent Achievements
        </h3>
        
        {data.achievements.length === 0 ? (
          <div className="text-center py-8">
            <Award className="mx-auto h-12 w-12 text-dark-400 mb-4" />
            <h4 className="text-lg font-medium text-white mb-2">No achievements yet</h4>
            <p className="text-dark-400">Keep studying to unlock your first achievement!</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {data.achievements.map((achievement) => (
              <div 
                key={achievement.id} 
                className={`bg-dark-700 rounded-lg p-4 border-2 ${getRarityColor(achievement.rarity)}`}
              >
                <div className="flex items-center gap-3 mb-3">
                  <div className="text-2xl">{achievement.icon}</div>
                  <div className="flex-1">
                    <h4 className="text-white font-medium">{achievement.name}</h4>
                    <p className="text-dark-400 text-sm">{achievement.description}</p>
                  </div>
                </div>
                <div className="flex items-center justify-between text-xs">
                  <span className={`px-2 py-1 rounded ${getRarityColor(achievement.rarity)} bg-opacity-20`}>
                    {achievement.rarity}
                  </span>
                  <span className="text-dark-400">
                    {new Date(achievement.earned_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default AnalyticsPage;
