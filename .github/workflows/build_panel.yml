name: Build Frontend For Panel

on:
  push:
    branches:
      - main

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source code
        uses: actions/checkout@v3

      # Set up Node.js for frontend build
      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: panel/frontend/package-lock.json

      # Build frontend
      - name: Install frontend dependencies
        run: |
          cd panel/frontend
          npm install

      - name: Build frontend
        run: |
          cd panel/frontend
          npm run build

      # Create dist directory in backend if it doesn't exist
      - name: Prepare backend dist directory
        run: |
          mkdir -p panel/backend/dist

      # Copy frontend build to backend/dist
      - name: Copy frontend build to backend
        run: |
          cp -r panel/frontend/build/* panel/backend/dist/

      # Clean up .txt files except robots.txt
      - name: Clean up .txt files
        run: |
          find panel/backend/dist -name "*.txt" -not -name "robots.txt" -delete
